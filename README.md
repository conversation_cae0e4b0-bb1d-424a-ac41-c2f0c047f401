# Mexel Lead Discovery System

An AI-powered marketing system with tender monitoring capabilities for Mexel Energy Sustain's chemical products.

## 🚀 Quick Start

### First Time Setup

```bash
./setup.sh
```

### Start the Application

```bash
./start.sh
```

The application will be available at:

- **Frontend**: http://localhost:3000 (React Dashboard)
- **Backend**: http://localhost:3001 (Python API)

## 📁 Project Structure

```
mexel/
├── apps/
│   ├── frontend/          # React dashboard (port 3000)
│   └── backend/           # Python FastAPI (port 3001)
├── shared/                # Shared TypeScript code
├── data/                  # SQLite database
├── scripts/               # Utility scripts
├── start.sh              # One-command startup
└── setup.sh              # Initial setup
```

## Features

### Core Functionality

- Automated tender opportunity monitoring in South Africa
- Lead generation and tracking
- Website traffic and social media engagement metrics
- Daily performance reporting with SQLite analytics
- Configurable keyword monitoring
- Email notifications for high-priority opportunities

### Testing

The project uses several testing approaches:

- Unit tests with Jest
- Integration tests for API endpoints
- End-to-end (E2E) testing using Playwright for Node.js
  - See [E2E Testing Guide](E2E_TESTING_GUIDE.md) for more details

### Enhanced Coordination Features

- AI-driven task prioritization and workflow optimization
- Intelligent performance monitoring and analysis
- Automated recommendations using Deepseek AI integration
- Real-time task assessment and reallocation
- Performance metrics stored in SQLite database
- Dynamic workflow rules based on system performance

## Setup

### Prerequisites

- Node.js 16.20.0 (recommended)
- npm 8.x or higher
- Docker and Docker Compose (optional, for containerized development)

### Node.js Version Management

This project is configured to use Node.js 16.20.0. You can use nvm or nodenv to manage your Node.js version:

```bash
# Using nvm
nvm install
nvm use

# Using nodenv
nodenv install
```

### Standard Setup

1. Clone this repository
2. Copy `.env.example` to `.env` and fill in your configuration values:
   - Email settings
   - Deepseek API key (for AI-powered recommendations)
   - SQLite database path (defaults to data/mexel.db)
   - Performance targets and thresholds
3. Install dependencies:

```bash
# Install root dependencies
yarn install

# Install frontend dependencies
cd apps/frontend
yarn install
cd ..
```

4. Create the data directory for SQLite:

```bash
mkdir -p data
```

5. Build the project:

```bash
yarn build
```

6. Initialize the database (this happens automatically on first run)

7. Start the system:

```bash
# Start the backend
yarn start

# In a separate terminal, start the frontend
yarn frontend:start
```

For development:

```bash
# Start the backend in development mode
yarn dev

# In a separate terminal, start the frontend in development mode
yarn frontend:start
```

### Docker Setup

This application is containerized using Docker and can be run using Docker Compose.

#### Prerequisites

- Docker
- Docker Compose

#### Development Setup

1. Clone the repository:

   ```bash
   git clone <repository-url>
   cd Mexel
   ```

2. Build and start the development environment:
   ```bash
   docker-compose -f docker-compose.dev.yml up --build
   ```

This will start:

- Frontend at http://localhost:3000
- Backend API at http://localhost:3001
- PostgreSQL database at localhost:5432

#### Development Notes

- The application uses hot-reloading in development mode
- Frontend and backend code changes will automatically reflect in the running application
- Database data persists between container restarts in a Docker volume

#### Common Commands

```bash
# Start the development environment
docker-compose -f docker-compose.dev.yml up

# Rebuild and start (after dependencies change)
docker-compose -f docker-compose.dev.yml up --build

# Stop all containers
docker-compose -f docker-compose.dev.yml down

# View logs
docker-compose -f docker-compose.dev.yml logs -f

# View logs for a specific service
docker-compose -f docker-compose.dev.yml logs -f [service_name]
```

#### Troubleshooting

If you encounter any issues:

1. Clean up Docker resources:

   ```bash
   docker-compose -f docker-compose.dev.yml down --volumes
   docker builder prune -f
   ```

2. Rebuild the containers:
   ```bash
   docker-compose -f docker-compose.dev.yml up --build
   ```

### Environment Verification

After setup, verify your environment is working correctly:

```bash
# Run full environment verification
./verify_environment.sh

# Verify Python environment only
python3 scripts/verify_python_env.py

# Test MCP settings schema
python3 apps/backend/tests/test_mcp_settings.py
```

The verification scripts will check:

- ✅ Docker services and container health
- ✅ Python 3.11.12 and required packages
- ✅ Database connectivity
- ✅ API endpoints and schema validation
- ✅ Project structure and configuration

For detailed verification instructions, see the [Environment Verification Guide](docs/ENVIRONMENT_VERIFICATION_GUIDE.md).

### Troubleshooting

If you encounter issues with dependencies:

1. Clean node_modules:

```bash
# Remove all node_modules directories
find . -name "node_modules" -type d -prune -exec rm -rf {} \; 2>/dev/null || true

# Remove all package-lock.json files
find . -name "package-lock.json" -type f -delete
```

2. Reinstall dependencies:

```bash
yarn install
cd apps/frontend
yarn install
cd ..
```

## Configuration

Configure the system through the `.env` file:

### Core Settings

- Email configuration
- API keys
- Monitoring intervals
- Notification preferences

### Performance Targets

- Lead generation: 50+ qualified leads/month
- Website traffic: 200% increase
- Social engagement: 150% increase
- Tender opportunities: 5+ relevant tenders/month

### Task Priority Configuration

- High-priority tender score threshold
- Performance monitoring thresholds
- Task assessment intervals

## Intelligent Coordination

The system uses an enhanced Coordinator Agent that:

- Dynamically prioritizes tasks based on performance metrics
- Monitors agent performance and adjusts task allocation
- Generates AI-powered recommendations for improvement
- Maintains a performance dashboard in SQLite
- Implements conditional workflows for high-value opportunities

## Tender Monitoring

The system monitors the following tender sources:

- etenders.gov.za (South African government tenders)
- eskom.co.za/suppliers/tenders/ (Eskom-specific tenders)

Keywords and monitoring rules are configurable through the spider files.

### Using Real Data

The system is configured to use real data from eTenders and Eskom. To scrape and use real data:

1. Run the scraper:

   ```bash
   yarn scrape
   ```

   This will scrape tender data from eTenders and Eskom and store it in the SQLite database.

2. Access the data through the API:
   - GET `/api/tenders` - Returns all tenders
   - POST `/api/tenders/refresh` - Triggers a new scraping process

The system will automatically use real data if available. If no real data is found, it will fall back to mock data.

## Performance Tracking

The system maintains detailed performance metrics in a SQLite database:

- Task completion rates and execution times
- Agent performance statistics
- Lead generation and conversion metrics
- Tender opportunity success rates
- Real-time performance against targets

## Integration Options

The system supports integration with:

- Deepseek for intelligent recommendations
- SQLite for robust data storage
- Zapier/n8n for workflow automation
- Email services for notifications
- Docker for containerized deployment and development
- Node.js 16.20.0 for consistent runtime environment

## Intelligent Lead Management

The system uses SQLite as a robust CRM solution for:

- Lead tracking and management
- Email engagement monitoring
- Campaign performance analytics
- Activity logging
- Automated sequence management

## Package Manager

This project uses Yarn as its package manager. Please ensure you have Yarn installed before working with this project.

### Installation

To install Yarn, follow the instructions at [https://yarnpkg.com/getting-started/install](https://yarnpkg.com/getting-started/install).

### Commands

- Install dependencies: `yarn install`
- Run the development server: `yarn start`
- Run tests: `yarn test`

**Note:** Using npm commands will result in errors. Please use Yarn for all package management operations.

## Project Organization

The Mexel project follows a structured organization for better maintainability:

### Directory Structure

```
/
├── apps/backend/    # Python FastAPI backend service
├── apps/frontend/         # React frontend application
├── scripts/          # Utility and automation scripts (organized by category)
│   ├── docker/       # Container management scripts
│   ├── websocket/    # WebSocket testing and monitoring
│   ├── maintenance/  # System maintenance scripts
│   ├── testing/      # Test automation scripts
│   └── development/  # Development environment scripts
├── docs/             # Project documentation
├── data/             # Data files and local databases
└── docker/           # Docker-specific configurations
```

### Key Documentation

- [Project Organization Standards](docs/PROJECT_ORGANIZATION.md) - Overall project structure
- [Package Management Guide](docs/PACKAGE_MANAGEMENT_GUIDE.md) - Dependency management practices
- [Docker Configuration Guide](docs/DOCKER_CONFIGURATION_GUIDE.md) - Docker best practices
- [WebSocket Integration Guide](docs/WEBSOCKET_INTEGRATION_GUIDE.md) - Real-time communication
- [WebSocket Testing Guide](docs/WEBSOCKET_TESTING_GUIDE.md) - Testing WebSocket connections

### Development Tools

For streamlined development, use these utility scripts:

- **dev-master.sh** - Master development script for common tasks

  ```bash
  ./dev-master.sh           # Show help
  ./dev-master.sh start     # Start development environment
  ./dev-master.sh test      # Run tests
  ./dev-master.sh logs      # View container logs
  ```

- **websocket-tool.sh** - Comprehensive WebSocket testing tool
  ```bash
  ./websocket-tool.sh       # Show help
  ./websocket-tool.sh test  # Test WebSocket connections
  ./websocket-tool.sh health # Check WebSocket health
  ```

For more details on the project structure and organization standards, see the [Project Organization Standards](docs/PROJECT_ORGANIZATION.md) document.
