#!/bin/zsh
# Description: <PERSON>ript to fix Material UI dependency issues in the frontend container
# Date: 2025-05-29

# Colors for terminal output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Fixing Material UI dependency issues in frontend container...${NC}"

# Step 1: Stop the frontend container
echo -e "${BLUE}Stopping frontend container...${NC}"
docker-compose -f docker-compose.dev.yml stop frontend

# Step 2: Revert package.json script changes (from react-app-rewired back to react-scripts)
echo -e "${BLUE}Updating package.json...${NC}"
sed -i "" 's/"start": "react-app-rewired start",/"start": "react-scripts start",/g' ./frontend/package.json
sed -i "" 's/"build": "react-app-rewired build",/"build": "react-scripts build",/g' ./frontend/package.json

# Step 3: Update Material UI dependencies
echo -e "${BLUE}Updating Material UI dependencies...${NC}"
sed -i "" 's/"@mui\/icons-material": "\^7.1.0",/"@mui\/icons-material": "^5.15.15",/g' ./frontend/package.json
sed -i "" 's/"@mui\/x-date-pickers": "\^8.4.0",/"@mui\/x-date-pickers": "^6.19.5",/g' ./frontend/package.json

# Step 4: Rebuild and restart the frontend container
echo -e "${BLUE}Rebuilding frontend container...${NC}"
docker-compose -f docker-compose.dev.yml build frontend

echo -e "${BLUE}Restarting frontend container...${NC}"
docker-compose -f docker-compose.dev.yml up -d frontend

echo -e "${GREEN}Fix applied! Checking frontend container status...${NC}"
sleep 10
docker-compose -f docker-compose.dev.yml ps frontend

echo -e "${YELLOW}Viewing logs to verify the fix...${NC}"
docker-compose -f docker-compose.dev.yml logs --tail=20 frontend

echo -e "${GREEN}Done! If the container is still restarting, check logs with:${NC}"
echo -e "${YELLOW}docker-compose -f docker-compose.dev.yml logs -f frontend${NC}"
