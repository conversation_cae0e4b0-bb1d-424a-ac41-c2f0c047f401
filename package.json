{"name": "mexel", "version": "1.0.0", "private": true, "workspaces": ["apps/frontend", "shared"], "scripts": {"start": "./start.sh", "setup": "./setup.sh", "dev": "concurrently \"yarn dev:frontend\" \"yarn dev:backend\"", "dev:frontend": "cd apps/frontend && yarn start", "dev:backend": "cd apps/backend && source venv/bin/activate && python simple_main.py", "frontend": "cd apps/frontend && yarn start", "backend": "cd apps/backend && source venv/bin/activate && python simple_main.py", "build": "cd apps/frontend && yarn build", "test": "cd apps/frontend && yarn test", "lint": "eslint apps/frontend/src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint apps/frontend/src --ext .js,.jsx,.ts,.tsx --fix", "clean": "rm -rf apps/frontend/build apps/frontend/dist node_modules/.cache"}, "packageManager": "yarn@4.9.2", "installConfig": {"hoistingLimits": "workspaces", "pnp": false}, "engines": {"node": ">=18.0.0", "yarn": ">=4.0.0"}, "dependencies": {"typescript": "4.9.5"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/plugin-transform-runtime": "^7.27.4", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@types/babel__core": "^7", "@types/babel__plugin-transform-runtime": "^7", "@types/babel__preset-env": "^7", "@types/node": "^18.19.14", "@types/react-refresh": "^0", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "babel-plugin-transform-react-remove-prop-types": "^0.4.24", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "husky": "^8.0.3", "jscpd": "^4.0.5", "lint-staged": "^15.2.0", "react-refresh": "^0.17.0"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}}