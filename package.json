{"name": "mexel", "version": "1.0.0", "private": true, "workspaces": {"packages": ["apps/frontend", "apps/backend", "shared"]}, "scripts": {"postmigrate": "node ./tools/scripts/validate-migration.js", "start": "./start-dashboard.sh", "start:frontend": "cd apps/frontend && yarn start", "start:backend": "cd apps/backend && source venv/bin/activate && python3.11 simple_main.py", "dev": "concurrently \"yarn dev:frontend\" \"yarn dev:backend\"", "dev:frontend": "cd apps/frontend && yarn dev", "dev:backend": "cd apps/backend && source venv/bin/activate && python3.11 simple_main.py", "build": "yarn workspaces run build", "test": "yarn workspaces run test", "lint": "yarn workspaces run lint", "format": "yarn workspaces run format", "clean": "yarn workspaces run clean", "prepare": "husky install", "check:duplicates": "jscpd . --reporters console<PERSON>ull", "check:duplicates:html": "jscpd . --reporters html --output coverage/jscpd-report", "generate-certs": "bash scripts/generate-certs.sh", "setup-ssl": "yarn generate-certs && echo 'SSL certificates generated. Check scripts/generate-certs.sh output for instructions to trust the CA certificate.'"}, "packageManager": "yarn@4.9.2", "installConfig": {"hoistingLimits": "workspaces", "pnp": false}, "engines": {"node": ">=18.0.0", "yarn": ">=4.0.0"}, "dependencies": {"typescript": "4.9.5"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/plugin-transform-runtime": "^7.27.4", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@types/babel__core": "^7", "@types/babel__plugin-transform-runtime": "^7", "@types/babel__preset-env": "^7", "@types/node": "^18.19.14", "@types/react-refresh": "^0", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "babel-plugin-transform-react-remove-prop-types": "^0.4.24", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "husky": "^8.0.3", "jscpd": "^4.0.5", "lint-staged": "^15.2.0", "react-refresh": "^0.17.0"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}}