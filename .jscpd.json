{"$schema": "./schemas/jscpd.json", "threshold": 2, "pattern": ["apps/frontend/src/**/*.{ts,tsx}", "apps/frontend/src/**/*.{js,jsx}", "apps/backend/**/*.py", "shared/**/*.{ts,tsx}", "scripts/**/*.{js,ts}", "**/*.json", "**/*.config.{js,ts}"], "ignore": ["**/node_modules/**", "**/dist/**", "**/build/**", "**/coverage/**", "**/.yarn/**", "**/venv-3.11/**", "**/*.min.js", "**/*.d.ts", "**/schemas/**", ".next/**", "**/temp-frontend/**", "**/__tests__/**", "**/*.test.{ts,tsx,js,jsx}", "**/*.spec.{ts,tsx,js,jsx}", "**/tests/**", "**/mocks/**", "apps/frontend/backups/**", "apps/frontend/exports/**"], "format": ["json", "html", "markdown"], "output": "./coverage/jscpd-report", "reporters": ["html", "consoleFull", "json"], "minLines": 3, "minTokens": 35, "blame": true, "mode": "strict", "maxSize": "10MB", "maxLines": 500, "silent": false, "debug": false, "skipLexical": true, "detectIdentifiers": true}