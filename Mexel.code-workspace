{"folders": [{"path": "."}], "settings": {"jest.rootPath": "frontend", "jest.jestCommandLine": "npm test", "jest.runMode": {"type": "on-demand", "clearOnRun": "none", "revealOutput": "on-run"}, "python.linting.enabled": true, "python.analysis.extraPaths": ["${workspaceFolder}/backend-python", "${workspaceFolder}/test_venv/lib/python3.11/site-packages"], "python.defaultInterpreterPath": "${workspaceFolder}/test_venv/bin/python3.11", "python.analysis.typeCheckingMode": "basic", "python.analysis.autoImportCompletions": true, "[python]": {"editor.formatOnSave": true, "editor.defaultFormatter": "ms-python.python"}, "files.associations": {"docker-compose*.yml": "dockercompose"}, "yaml.schemas": {"https://raw.githubusercontent.com/compose-spec/compose-spec/master/schema/compose-spec.json": "docker-compose*.yml"}}}