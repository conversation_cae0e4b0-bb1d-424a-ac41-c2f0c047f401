#!/bin/bash

# cleanup_duplicates.sh
# Script to clean up duplicates in the Mexel project based on the duplicate analysis report
# This script addresses the following recommendations:
# 1. Review and consolidate docker-compose files
# 2. Remove duplicate node_modules directories
# 3. Clean up virtual environments
# 4. Review and remove unnecessary backup files

set -e

echo "=== Mexel Duplicate Cleanup ==="
echo "This script will clean up duplicates identified in the duplicate analysis report."
echo "Please ensure you have a backup before proceeding."
echo ""

# Ask for confirmation before proceeding
read -p "Do you want to proceed with cleanup? (y/n): " confirm
if [[ $confirm != "y" && $confirm != "Y" ]]; then
    echo "Operation canceled."
    exit 0
fi

# Function to display section headers
section() {
    echo ""
    echo "=== $1 ==="
}

# 1. Consolidate docker-compose files
section "Consolidating docker-compose files"

# Create a backup of original docker-compose files
echo "Creating backup of docker-compose files..."
mkdir -p .docker-compose-backup
cp docker-compose*.yml .docker-compose-backup/ 2>/dev/null || true
echo "Docker-compose files backed up to .docker-compose-backup/"

# Copy the consolidated file to the root
echo "Using consolidated docker-compose file as the primary file..."
cp scripts/docker/compose/docker-compose.consolidated.yml docker-compose.yml
echo "Consolidated docker-compose.yml is now in the project root."

# Keep the dev and simple variants but update them
echo "Updating specialized docker-compose variants..."
cp scripts/docker/compose/docker-compose.dev.yml docker-compose.dev.yml 2>/dev/null || true
cp scripts/docker/compose/docker-compose.minimal.yml docker-compose.simple.yml 2>/dev/null || true

echo "Docker-compose files consolidated. Original files are in .docker-compose-backup/"

# 2. Remove duplicate node_modules directories
section "Cleaning up node_modules directories"

echo "Keeping essential node_modules and removing duplicates..."

# Remove temp-frontend node_modules
if [ -d "frontend/temp-frontend/node_modules" ]; then
    echo "Removing frontend/temp-frontend/node_modules directory..."
    rm -rf frontend/temp-frontend/node_modules
    echo "Removed frontend/temp-frontend/node_modules"
fi

echo "Node_modules cleanup completed."

# 3. Clean up virtual environments
section "Cleaning up virtual environments"

echo "Consolidating virtual environments..."

# For backend-python, keep only one virtual environment
if [ -d "backend-python/venv" ] && [ -d "backend-python/.venv" ]; then
    echo "Found duplicate virtual environments in backend-python."
    echo "Keeping backend-python/.venv and removing backend-python/venv..."
    rm -rf backend-python/venv
    echo "Removed backend-python/venv"
fi

echo "Virtual environments cleanup completed."

# 4. Remove backup files
section "Removing backup files"

echo "Removing identified backup files..."

# Remove explicit backup files
find . -type f \( -name "*.bak" -o -name "*.backup" -o -name "*.old" -o -name "*.fixed" -o -name "*.clean" \) -not -path "*/node_modules/*" | while read file; do
    echo "Removing backup file: $file"
    rm "$file"
done

# Handle backup files in node_modules separately
find . -path "*/node_modules/*" -type f \( -name "*.bak" -o -name "*.backup" -o -name "*.old" -o -name "*.fixed" -o -name "*.clean" \) | while read file; do
    echo "Found backup file in node_modules: $file"
    # We'll skip these as they might be part of the package
    echo "  (Skipping as it might be part of the package)"
done

echo "Backup files cleanup completed."

section "Cleanup Summary"
echo "1. Docker-compose files consolidated"
echo "2. Duplicate node_modules directories removed"
echo "3. Virtual environments consolidated"
echo "4. Backup files removed"
echo ""
echo "Cleanup process completed successfully!"