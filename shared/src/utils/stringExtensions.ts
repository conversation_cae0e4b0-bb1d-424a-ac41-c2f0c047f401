/**
 * String prototype extensions implementation
 * This file implements the 'text' property for string types
 */

// Extend String prototype with the 'text' property
Object.defineProperty(String.prototype, 'text', {
  get() {
    return this.toString();
  },
  enumerable: false,
  configurable: true
});

// We need to ensure the String interface knows about our extension
declare global {
  interface String {
    /**
     * Gets the string value.
     * This property provides direct access to the string value of any string type.
     */
    readonly text: string;
  }
}

export {};