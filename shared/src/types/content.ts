// Enum for Content Type
export enum ContentType {
  BLOG_POST = 'blog_post',
  LINKEDIN_POST = 'linkedin_post',
  INFOGRAPHIC = 'infographic',
  EMAIL_NEWSLETTER = 'email_newsletter',
  CASE_STUDY = 'case_study',
}

// Enum for Content Status
export enum ContentStatus {
  DRAFT = 'draft',
  REVIEW = 'review',
  APPROVED = 'approved',
  PUBLISHED = 'published',
  REJECTED = 'rejected',
}

// Interface for Content Item
export interface ContentItem {
  id: string;
  title: string;
  type: ContentType;
  content: string;
  summary?: string;
  keywords: string[];
  status: ContentStatus;
  createdAt: Date | string; // Allow string for API response, convert to Date in frontend
  updatedAt: Date | string; // Allow string for API response, convert to Date in frontend
  publishedAt?: Date | string; // Allow string for API response, convert to Date in frontend
  author?: string;
  imagePrompt?: string;
  imageUrl?: string;
  metadata?: Record<string, unknown>;
}

// Interface for Schedule Item
export interface ScheduleItem {
  id: string;
  contentId?: string;
  type: ContentType;
  topic?: string;
  keywords?: string[];
  scheduledDate: Date | string | null; // Allow null
  publishedAt?: Date | string | null; // Allow null
  status: 'scheduled' | 'generated' | 'published' | 'failed';
  createdAt: Date | string; // Allow string for API response, convert to Date in frontend
  updatedAt: Date | string; // Allow string for API response, convert to Date in frontend
  notificationSent: boolean;
  error?: string;
}

// Interface for Content Generation Request
export interface ContentGenerationRequest {
  type: ContentType;
  topic?: string;
  keywords?: string[];
  targetAudience?: string;
  tone?: string;
  length?: 'short' | 'medium' | 'long';
  includeImagePrompt?: boolean;
  additionalInstructions?: string;
}

// Interface for Schedule Content Request
export interface ScheduleContentRequest {
  type: ContentType;
  topic?: string;
  keywords?: string[];
  scheduledDate: Date | string | null; // Allow null
  targetAudience?: string;
  tone?: string;
  length?: 'short' | 'medium' | 'long';
  includeImagePrompt?: boolean; // Added this line
  additionalInstructions?: string;
}
