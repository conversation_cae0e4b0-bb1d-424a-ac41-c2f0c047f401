export type AgentRole = 'COORDINATOR' | 'TENDER_MONITOR' | 'EMAIL' | 'ANALYTICS' | 'LEAD_DISCOVERY';

export interface AgentMetrics {
  id: string;
  name: string;
  role: AgentRole;
  status: 'idle' | 'running' | 'error';
  successRate: number;
  errorRate: number;
  latency: number;
  lastCheckTime: string;
}

export interface TaskResult {
  success: boolean;
  data?: unknown;
  error?: string;
}

export interface PerformanceMetrics {
  leadsGenerated: number;
  websiteTraffic: number;
  socialEngagement: number;
  tenderOpportunities: number;
  lastUpdated: Date;
}