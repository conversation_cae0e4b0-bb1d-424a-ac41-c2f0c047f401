export enum TenderStatus {
    NEW = 'NEW',
    PROCESSED = 'PROCESSED',
    IN_PROGRESS = 'IN_PROGRESS',
    COMPLETED = 'COMPLETED',
    SUBMITTED = 'SUBMITTED',
    REJECTED = 'REJECTED'
}

export interface TenderDocument {
    title: string;
    url: string;
}

export interface Tender {
    id: string;
    title: string;
    description: string;
    url: string;
    documents?: TenderDocument[];
    status?: TenderStatus;
}

export interface ScrapedTender extends Tender {
    source: string;
    scrapedAt: Date;
    rawData?: any;
}

export interface TenderOpportunity extends Tender {
    value?: number;
    deadline?: Date;
    category?: string;
    requirements?: string[];
    isQualified?: boolean;
}

// Example usage:
// const tender: TenderOpportunity = {
//     id: '123',
//     title: 'Example Tender',
//     description: 'Description here',
//     url: 'https://example.com',
//     status: TenderStatus.NEW
// };