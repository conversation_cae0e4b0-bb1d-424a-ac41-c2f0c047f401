export interface AnalyticsData {
  id?: string;
  timestamp?: Date;
  eventType?: string;
  source?: string;
  metadata?: Record<string, any>;
  
  // Legacy fields for backward compatibility
  leadsGenerated: number;
  websiteTraffic: number;
  socialEngagement: number;
  tenderOpportunities: number;
  conversionRate: number;
  averageTenderScore: number;
  topKeywords: string[];
  lastUpdated: string;
}

// Keeping the original interfaces for backward compatibility
export interface LegacyAnalyticsData {
  leadsGenerated: number;
  websiteTraffic: number;
  socialEngagement: number;
  tenderOpportunities: number;
  conversionRate: number;
  averageTenderScore: number;
  topKeywords: string[];
  lastUpdated: string;
}

export interface TenderMetrics {
  totalOpportunities: number;
  highPriorityCount: number;
  average_relevance_score: number;
  responseRate: number;
}

export interface Insight {
  type: string;
  description: string;
  metrics: Array<{ name: string; value: unknown }>;
  recommendation?: string;
}