{"$schema": "../.vscode/schemas/package.schema.json", "name": "@mexel/shared", "version": "0.1.0", "private": true, "main": "dist/index.js", "types": "dist/index.d.ts", "type": "commonjs", "scripts": {"build": "tsc", "dev": "tsc --watch"}, "dependencies": {"anymatch": "3.1.3", "axios": "^1.6.2", "common-tags": "1.8.2", "detect-port-alt": "1.1.6", "eventsource": "^2.0.2", "glob-parent": "6.0.2", "is-root": "3.0.0", "jsonfile": "6.1.0", "jsonpointer": "5.0.1", "kleur": "4.1.5", "react-router": "6.22.1", "react-router-dom": "6.22.1", "schema-utils": "4.3.2", "to-regex-range": "5.0.1"}, "devDependencies": {"@types/eventsource": "^1.1.15", "@types/socket.io-client": "^3.0.0", "typescript": "^4.9.0"}, "peerDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}}