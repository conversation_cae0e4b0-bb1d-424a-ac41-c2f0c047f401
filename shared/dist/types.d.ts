export interface BaseEntity {
    id: string;
    createdAt: string;
    updatedAt: string;
}
export interface User extends BaseEntity {
    email: string;
    name: string;
    role: 'admin' | 'user';
}
export interface Agent extends BaseEntity {
    name: string;
    status: 'active' | 'inactive' | 'error';
    type: string;
    lastActivity?: string;
}
export interface TenderOpportunity extends BaseEntity {
    title: string;
    description: string;
    score: number;
    status: 'active' | 'inactive' | 'completed';
    dueDate: string;
}
export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    error?: string;
    message?: string;
}
export interface PaginationParams {
    page: number;
    limit: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}
export interface PaginatedResponse<T> extends ApiResponse<T[]> {
    pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
    };
}
//# sourceMappingURL=types.d.ts.map