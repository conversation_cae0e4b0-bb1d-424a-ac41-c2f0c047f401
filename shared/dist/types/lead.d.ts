export declare enum LeadStatus {
    NEW = "NEW",
    CONTACTED = "CONTACTED"
}
export interface Lead {
    id: string;
    email: string;
    status: LeadStatus;
}
export interface ExtendedLead extends Lead {
    firstName?: string;
    lastName?: string;
    company?: string;
    companyName?: string;
    contactName?: string;
    position?: string;
    industry?: string;
    lastContactDate?: Date;
    recentActivity?: string;
    companySize?: string;
    contactRole?: string;
    region?: string;
    systems?: string[];
}
//# sourceMappingURL=lead.d.ts.map