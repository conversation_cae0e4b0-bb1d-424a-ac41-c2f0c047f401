/**
 * Shared email interfaces for the Mexel project
 * This file contains all the email-related interfaces that are used across the application
 */
/**
 * Basic email message interface
 */
export interface IEmailMessage {
    to: string;
    subject: string;
    body: string;
    from?: string;
    metadata?: Record<string, any>;
}
/**
 * Email template interface
 */
export interface IEmailTemplate {
    name?: string;
    subject: string;
    body: string;
    metadata?: Record<string, any>;
    emailId?: string;
    engagement?: IEmailEngagement;
    followUpScheduleDays?: number[];
    abTestCampaignId?: string;
}
/**
 * Comprehensive email interface
 */
export interface IEmail {
    id: string;
    to: string;
    subject: string;
    content: string;
    leadId: string;
    sequenceStep: number;
    totalSteps: number;
    strategyType: string;
    status: 'pending' | 'sent' | 'delivered' | 'failed';
    createdAt: Date;
    updatedAt: Date;
}
/**
 * Email metadata interface
 */
export interface IEmailMetadata {
    leadId: string;
    sequenceId?: string;
    type: string;
    step?: number;
}
/**
 * Email sequence interface
 */
export interface IEmailSequence {
    leadId: string;
    steps: IEmailSequenceStep[];
    currentStep: number;
    status: 'active' | 'completed' | 'paused';
    type: 'tender' | 'discovery' | 'nurture';
}
/**
 * Email sequence step interface
 */
export interface IEmailSequenceStep {
    template: IEmailTemplate;
    scheduledDate: Date;
    sent: boolean;
    engagement?: IEmailEngagement;
}
/**
 * Email engagement interface
 */
export interface IEmailEngagement {
    emailId: string;
    leadId: string;
    type: 'open' | 'click' | 'reply';
    timestamp: Date;
    metadata?: Record<string, unknown>;
}
/**
 * Email activity interface
 */
export interface IEmailActivity {
    leadId: string;
    emailId: string;
    template: IEmailTemplate;
    status: 'sent' | 'opened' | 'clicked' | 'replied' | 'bounced';
    timestamp: Date;
}
/**
 * Email options interface
 */
export interface IEmailOptions {
    to: string;
    subject: string;
    body: string;
    scheduledDate?: Date;
    textContent?: string;
    tags?: string[];
    metadata?: {
        type?: string;
        step?: number;
        sequenceId?: string;
        leadId?: string;
        strategyType?: string;
        [key: string]: any;
    };
}
/**
 * Queued email interface
 */
export interface IQueuedEmail {
    to: string;
    subject: string;
    body: string;
    metadata: {
        sequenceId: string;
        step: number;
        type: string;
        [key: string]: any;
    };
    scheduledDate?: Date;
}
/**
 * Email response interface
 */
export interface IEmailResponse {
    success: boolean;
    messageId?: string;
    error?: Error;
    queuedEmail: IQueuedEmail;
    metadata?: Record<string, any>;
}
/**
 * Email strategy interface
 */
export interface IEmailStrategy {
    type: string;
    frequencyDays: number[];
    subjectLine: string;
    openingLine: string;
    closingLine: string;
    callToAction: string;
}
/**
 * Product match interface for email personalization
 */
export interface IProductMatch {
    industry: string;
    product: string;
    benefits: string[];
    painPoints: string[];
    caseStudies?: string[];
    technicalSpecs?: Record<string, string>;
}
/**
 * Email metrics interface
 */
export interface IEmailMetrics {
    sent: number;
    delivered: number;
    opens: number;
    clicks: number;
    replies: number;
    unsubscribes: number;
    openRate: number;
    clickRate: number;
    replyRate: number;
}
export interface IEmailAnalyticsData {
    campaignId?: string;
    templateId?: string;
    overallMetrics: IEmailMetrics;
    timelineData?: Array<{
        date: string;
        sent: number;
        opened: number;
        clicked: number;
    }>;
    engagementByDevice?: Record<string, number>;
    engagementByLocation?: Record<string, number>;
}
/**
 * Email analytics interface for dashboard
 */
export interface IEmailAnalytics {
    sent: number;
    delivered: number;
    opened: number;
    clicked: number;
    replied: number;
    bounced: number;
    openRate: number;
    clickRate: number;
    replyRate: number;
    bounceRate: number;
    byIndustry: {
        [industry: string]: {
            sent: number;
            opened: number;
            clicked: number;
        };
    };
    lastUpdated: string;
}
/**
 * Email campaign interface
 */
export interface ICampaign {
    id: string;
    name: string;
    status: string;
    sentDate?: string;
    analytics: {
        sent: number;
        opened: number;
        clicked: number;
        openRate: number;
        clickRate: number;
    };
}
//# sourceMappingURL=email.d.ts.map