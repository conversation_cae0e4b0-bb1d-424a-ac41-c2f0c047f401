export declare enum ContentType {
    BLOG_POST = "blog_post",
    LINKEDIN_POST = "linkedin_post",
    INFOGRAPHIC = "infographic",
    EMAIL_NEWSLETTER = "email_newsletter",
    CASE_STUDY = "case_study"
}
export declare enum ContentStatus {
    DRAFT = "draft",
    REVIEW = "review",
    APPROVED = "approved",
    PUBLISHED = "published",
    REJECTED = "rejected"
}
export interface ContentItem {
    id: string;
    title: string;
    type: ContentType;
    content: string;
    summary?: string;
    keywords: string[];
    status: ContentStatus;
    createdAt: Date | string;
    updatedAt: Date | string;
    publishedAt?: Date | string;
    author?: string;
    imagePrompt?: string;
    imageUrl?: string;
    metadata?: Record<string, unknown>;
}
export interface ScheduleItem {
    id: string;
    contentId?: string;
    type: ContentType;
    topic?: string;
    keywords?: string[];
    scheduledDate: Date | string | null;
    publishedAt?: Date | string | null;
    status: 'scheduled' | 'generated' | 'published' | 'failed';
    createdAt: Date | string;
    updatedAt: Date | string;
    notificationSent: boolean;
    error?: string;
}
export interface ContentGenerationRequest {
    type: ContentType;
    topic?: string;
    keywords?: string[];
    targetAudience?: string;
    tone?: string;
    length?: 'short' | 'medium' | 'long';
    includeImagePrompt?: boolean;
    additionalInstructions?: string;
}
export interface ScheduleContentRequest {
    type: ContentType;
    topic?: string;
    keywords?: string[];
    scheduledDate: Date | string | null;
    targetAudience?: string;
    tone?: string;
    length?: 'short' | 'medium' | 'long';
    includeImagePrompt?: boolean;
    additionalInstructions?: string;
}
//# sourceMappingURL=content.d.ts.map