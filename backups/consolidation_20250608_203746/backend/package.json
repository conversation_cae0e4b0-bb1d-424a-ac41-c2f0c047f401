{"name": "@mexel/backend", "version": "1.0.0", "description": "Backend API for Mexel tender management system", "main": "dist/app.js", "scripts": {"start": "node dist/app.js", "dev": "ts-node-dev --respawn --transpile-only src/app.ts", "build": "tsc", "test": "jest", "lint": "eslint . --ext .ts", "format": "prettier --write \"src/**/*.ts\""}, "dependencies": {"bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.0", "morgan": "^1.10.0", "socket.io": "^4.7.2"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/node": "^22.15.30", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "prettier": "^3.1.1", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}, "engines": {"node": ">=18.0.0"}}