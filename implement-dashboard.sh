#!/bin/bash

# Colors for terminal output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Print functions for consistent formatting
print_step() {
    echo -e "\n${BLUE}[STEP]${NC} $1"
}

print_info() {
    echo -e "${CYAN}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if a port is in use
check_port() {
    local port=$1
    if lsof -i :$port >/dev/null 2>&1; then
        return 0 # Port is in use
    else
        return 1 # Port is free
    fi
}

# Function to free up a port
free_port() {
    local port=$1
    if check_port $port; then
        print_warning "Port $port is in use. Attempting to free it..."
        kill -9 $(lsof -t -i :$port) 2>/dev/null
        sleep 2
        if check_port $port; then
            print_error "Failed to free port $port"
            return 1
        fi
    fi
    return 0
}

# Function to set up the backend
setup_backend() {
    print_step "Setting up backend environment"
    
    cd apps/backend || {
        print_error "Backend directory not found"
        return 1
    }

    # Check if virtual environment exists
    if [ ! -d "venv" ]; then
        print_info "Creating Python virtual environment..."
        python3.11 -m venv venv || {
            print_error "Failed to create virtual environment"
            return 1
        }
    fi

    # Activate virtual environment
    source venv/bin/activate || {
        print_error "Failed to activate virtual environment"
        return 1
    }

    # Install backend dependencies
    print_info "Installing backend dependencies..."
    pip install -r requirements_dashboard.txt || {
        print_error "Failed to install backend dependencies"
        return 1
    }

    cd ../..
    print_success "Backend setup complete"
    return 0
}

# Function to set up the frontend
setup_frontend() {
    print_step "Setting up frontend environment"
    
    cd apps/frontend || {
        print_error "Frontend directory not found"
        return 1
    }

    # Clear caches
    print_info "Clearing frontend caches..."
    rm -rf node_modules/.cache .cache-loader .eslintcache

    # Clean install dependencies
    print_info "Installing frontend dependencies..."
    rm -rf node_modules yarn.lock package-lock.json
    yarn install || {
        print_error "Failed to install frontend dependencies"
        return 1
    }

    cd ../..
    print_success "Frontend setup complete"
    return 0
}

# Function to start the backend
start_backend() {
    print_step "Starting backend service"
    
    cd apps/backend
    source venv/bin/activate
    
    python3.11 simple_main.py &
    BACKEND_PID=$!
    
    cd ../..

    # Wait for backend to start
    print_info "Waiting for backend to initialize..."
    sleep 5
    
    if ! kill -0 $BACKEND_PID 2>/dev/null; then
        print_error "Backend process failed to start"
        return 1
    fi

    # Verify backend is responding
    if curl -s http://localhost:3001/health >/dev/null; then
        print_success "Backend is running on port 3001 (PID: $BACKEND_PID)"
        return 0
    else
        print_error "Backend is not responding to health checks"
        return 1
    fi
}

# Function to start the frontend
start_frontend() {
    print_step "Starting frontend service"
    
    cd apps/frontend
    
    yarn start &
    FRONTEND_PID=$!
    
    cd ../..

    # Wait for frontend to start
    print_info "Waiting for frontend to initialize..."
    sleep 7
    
    if ! kill -0 $FRONTEND_PID 2>/dev/null; then
        print_error "Frontend process failed to start"
        return 1
    fi

    # Verify frontend is responding
    if curl -s http://localhost:3000 >/dev/null; then
        print_success "Frontend is running on port 3000 (PID: $FRONTEND_PID)"
        return 0
    else
        print_error "Frontend is not responding"
        return 1
    fi
}

# Cleanup function
cleanup() {
    print_step "Shutting down services"
    
    # Kill specific processes
    [[ -n $BACKEND_PID ]] && kill $BACKEND_PID 2>/dev/null
    [[ -n $FRONTEND_PID ]] && kill $FRONTEND_PID 2>/dev/null
    
    # Kill any remaining processes
    pkill -f "simple_main.py" 2>/dev/null
    pkill -f "react-scripts start" 2>/dev/null
    
    print_success "All services stopped"
    exit 0
}

# Main execution
main() {
    # Show banner
    echo -e "\n${GREEN}========================================${NC}"
    echo -e "${GREEN}       MEXEL DASHBOARD IMPLEMENTATION${NC}"
    echo -e "${GREEN}========================================${NC}\n"

    # Free required ports
    print_step "Checking port availability"
    for port in 3000 3001; do
        if ! free_port $port; then
            print_error "Failed to free port $port. Please stop any services using this port."
            exit 1
        fi
    done

    # Setup environments
    if ! setup_backend; then
        print_error "Backend setup failed"
        exit 1
    fi

    if ! setup_frontend; then
        print_error "Frontend setup failed"
        exit 1
    fi

    # Start services
    if ! start_backend; then
        print_error "Failed to start backend"
        cleanup
        exit 1
    fi

    if ! start_frontend; then
        print_error "Failed to start frontend"
        cleanup
        exit 1
    fi

    # Show success message
    echo -e "\n${GREEN}========================================${NC}"
    echo -e "${GREEN}✨ Dashboard successfully implemented!${NC}"
    echo -e "${GREEN}========================================${NC}"
    echo -e "${CYAN}Frontend${NC}: http://localhost:3000"
    echo -e "${CYAN}Backend API${NC}: http://localhost:3001"
    echo -e "${CYAN}Health Check${NC}: http://localhost:3001/health"
    echo -e "\nPress ${YELLOW}Ctrl+C${NC} to stop all services\n"

    # Wait for processes
    wait $BACKEND_PID $FRONTEND_PID
}

# Set up trap for cleanup
trap cleanup SIGINT SIGTERM

# Run main function
main