#!/bin/bash

# Mexel Dashboard Startup Script
# This script starts both backend and frontend services for the Mexel dashboard

# Colors for terminal output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[DASHBOARD] ${NC}$1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS] ${NC}$1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING] ${NC}$1"
}

print_error() {
    echo -e "${RED}[ERROR] ${NC}$1"
}

# Function to free up ports
free_ports() {
    print_status "Checking required ports..."
    
    # Check port 3000 (frontend)
    if lsof -i :3000 >/dev/null 2>&1; then
        print_warning "Port 3000 is in use. Attempting to free it..."
        kill -9 $(lsof -t -i :3000) 2>/dev/null
        sleep 2
        if lsof -i :3000 >/dev/null 2>&1; then
            print_error "Failed to free port 3000. Please manually kill the process using this port."
            return 1
        else
            print_success "Port 3000 freed successfully"
        fi
    else
        print_status "Port 3000 is available"
    fi
    
    # Check port 3001 (backend)
    if lsof -i :3001 >/dev/null 2>&1; then
        print_warning "Port 3001 is in use. Attempting to free it..."
        kill -9 $(lsof -t -i :3001) 2>/dev/null
        sleep 2
        if lsof -i :3001 >/dev/null 2>&1; then
            print_error "Failed to free port 3001. Please manually kill the process using this port."
            return 1
        else
            print_success "Port 3001 freed successfully"
        fi
    else
        print_status "Port 3001 is available"
    fi
    
    return 0
}

# Function to prepare frontend
prepare_frontend() {
    print_status "Preparing frontend..."
    
    # Clean frontend caches
    if [ -f "./scripts/clean-frontend-cache.sh" ]; then
        print_status "Cleaning frontend caches..."
        ./scripts/clean-frontend-cache.sh
    else
        print_status "Cleaning frontend caches manually..."
        cd apps/frontend
        rm -rf node_modules/.cache
        rm -rf .cache-loader
        rm -rf .eslintcache
        rm -rf .parcel-cache
        print_success "Frontend caches cleared"
        cd ../..
    fi
    
    # Fix webpack paths
    if [ -f "./scripts/fix-webpack-paths.sh" ]; then
        print_status "Fixing webpack paths..."
        ./scripts/fix-webpack-paths.sh
    fi
    
    # Check if node_modules exists, if not, install dependencies
    if [ ! -d "apps/frontend/node_modules" ]; then
        print_warning "Frontend dependencies not found. Installing..."
        if [ -f "./scripts/reinstall-frontend-deps.sh" ]; then
            ./scripts/reinstall-frontend-deps.sh
        else
            cd apps/frontend
            yarn install --force
            print_success "Frontend dependencies installed"
            cd ../..
        fi
    else
        print_status "Frontend dependencies already installed"
    fi
}

# Function to start backend
start_backend() {
    print_status "Starting backend on port 3001..."
    cd apps/backend
    
    # Check if virtual environment exists
    if [ ! -d "venv" ]; then
        print_error "Virtual environment not found in apps/backend/venv"
        print_status "Please create a virtual environment first:"
        print_status "cd apps/backend && python3.11 -m venv venv && source venv/bin/activate && pip install -r requirements.txt"
        cd ../..
        return 1
    fi
    
    # Activate virtual environment and start server
    source venv/bin/activate
    python3.11 simple_main.py &
    BACKEND_PID=$!
    cd ../..
    
    # Wait for backend to start
    print_status "Waiting for backend to initialize..."
    sleep 5
    
    # Check if backend process is still running
    if ! kill -0 $BACKEND_PID 2>/dev/null; then
        print_error "Backend failed to start"
        return 1
    fi
    
    # Test backend health
    if curl -s http://localhost:3001/health >/dev/null; then
        print_success "Backend is running (PID: $BACKEND_PID)"
        return 0
    else
        print_warning "Backend started but health check failed"
        return 1
    fi
}

# Function to start frontend
start_frontend() {
    print_status "Starting frontend on port 3000..."
    cd apps/frontend
    yarn start &
    FRONTEND_PID=$!
    cd ../..
    
    # Wait for frontend to start
    print_status "Waiting for frontend to initialize..."
    sleep 10
    
    # Check if frontend process is still running
    if ! kill -0 $FRONTEND_PID 2>/dev/null; then
        print_error "Frontend failed to start"
        return 1
    fi
    
    # Test frontend accessibility (this will return true even if the page is not fully loaded)
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 | grep -q "200\|304"; then
        print_success "Frontend is running (PID: $FRONTEND_PID)"
        return 0
    else
        print_warning "Frontend started but not accessible yet"
        print_status "Frontend may still be compiling. Check http://localhost:3000 manually."
        return 0  # Continue anyway as frontend might still be compiling
    fi
}

# Cleanup function
cleanup() {
    print_status "Stopping services..."
    
    # Stop backend
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null
    fi
    pkill -f "simple_main.py" 2>/dev/null
    
    # Stop frontend
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null
    fi
    pkill -f "react-scripts start" 2>/dev/null
    
    print_success "All services stopped"
    exit 0
}

# Set up trap to clean up on exit
trap cleanup SIGINT SIGTERM

# Main execution
echo -e "${GREEN}==========================================${NC}"
echo -e "${GREEN}     MEXEL DASHBOARD STARTUP SCRIPT      ${NC}"
echo -e "${GREEN}==========================================${NC}"

# Free up required ports
if ! free_ports; then
    print_error "Failed to free required ports. Exiting."
    exit 1
fi

# Prepare frontend
prepare_frontend

# Start backend
if ! start_backend; then
    print_error "Failed to start backend. Exiting."
    cleanup
    exit 1
fi

# Start frontend
if ! start_frontend; then
    print_error "Failed to start frontend. Stopping backend and exiting."
    cleanup
    exit 1
fi

# Dashboard is running
echo -e "${GREEN}==========================================${NC}"
echo -e "${GREEN}     MEXEL DASHBOARD IS RUNNING!         ${NC}"
echo -e "${GREEN}==========================================${NC}"
echo -e "Frontend: ${BLUE}http://localhost:3000${NC}"
echo -e "Backend API: ${BLUE}http://localhost:3001${NC}"
echo -e "Health check: ${BLUE}http://localhost:3001/health${NC}"
echo -e "API test: ${BLUE}http://localhost:3001/api/test${NC}"
echo -e "${GREEN}==========================================${NC}"
echo -e "Press ${YELLOW}Ctrl+C${NC} to stop all services"

# Keep the script running until Ctrl+C is pressed
wait $BACKEND_PID $FRONTEND_PID