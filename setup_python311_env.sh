#!/bin/bash

echo "Setting up Mexel Python Environment..."
echo "======================================="

# Determine script location for reference
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$SCRIPT_DIR"

# Check if Python 3.11 is installed
PYTHON311_PATH=$(ls -d /opt/homebrew/Cellar/python@3.11/*/bin/python3.11 2>/dev/null | head -1)

if [ -z "$PYTHON311_PATH" ]; then
    echo "❌ ERROR: Python 3.11 not found."
    echo "Please install Python 3.11 via Homebrew:"
    echo "  brew install python@3.11"
    exit 1
fi

echo "✅ Found Python 3.11: $PYTHON311_PATH"

# Check if virtual environment exists
if [ ! -d "$PROJECT_ROOT/venv-3.11" ]; then
    echo "Creating new virtual environment with Python 3.11..."
    "$PYTHON311_PATH" -m venv "$PROJECT_ROOT/venv-3.11"
else
    echo "✅ Virtual environment exists at $PROJECT_ROOT/venv-3.11"
fi

# Activate the virtual environment
source "$PROJECT_ROOT/venv-3.11/bin/activate"

# Verify activation
CURRENT_PYTHON=$(which python)
echo "Using Python: $CURRENT_PYTHON"
echo "Python version: $(python --version)"

# Install required packages if needed
if [ "$1" = "--install" ] || [ "$1" = "-i" ]; then
    echo "Installing required packages..."

    # Install backend dependencies
    if [ -f "$PROJECT_ROOT/backend-python/requirements.txt" ]; then
        echo "Installing backend dependencies..."
        pip install -r "$PROJECT_ROOT/backend-python/requirements.txt"
    fi

    # Install AI dependencies if they exist
    if [ -f "$PROJECT_ROOT/backend-python/requirements-ai.txt" ]; then
        echo "Installing AI dependencies..."
        pip install -r "$PROJECT_ROOT/backend-python/requirements-ai.txt"
    fi
fi

echo ""
echo "✨ Python 3.11 environment is now active!"
echo "When you're finished working, type 'deactivate' to exit the virtual environment."
echo ""
echo "To use this environment in VS Code:"
echo "1. Open the Command Palette (Cmd+Shift+P)"
echo "2. Search for 'Python: Select Interpreter'"
echo "3. Select the interpreter at: $PROJECT_ROOT/venv-3.11/bin/python"
echo ""
