#!/bin/bash

# Stop any running containers
echo "Stopping any running containers..."
docker-compose down

# Remove existing node_modules volume to prevent permission issues
echo "Removing node_modules volume..."
docker volume rm mexel_node_modules || true

# Build and start containers with clean state
echo "Starting development environment..."
docker-compose -f docker-compose.simple.yml up --build

# If the script is interrupted, stop containers
trap 'docker-compose down' INT TERM