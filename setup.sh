#!/bin/bash

echo "🔧 Setting up Mexel Development Environment..."
echo "=============================================="

# Check prerequisites
echo "📋 Checking prerequisites..."

# Check Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js not found. Please install Node.js 18+ first."
    exit 1
fi

# Check Yarn
if ! command -v yarn &> /dev/null; then
    echo "❌ Yarn not found. Please install Yarn first."
    exit 1
fi

# Check Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 not found. Please install Python 3.8+ first."
    exit 1
fi

echo "✅ Prerequisites check passed!"

# Install Node.js dependencies
echo ""
echo "📦 Installing Node.js dependencies..."
yarn install

# Setup Python backend
echo ""
echo "🐍 Setting up Python backend..."
cd apps/backend

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "Creating Python virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment and install dependencies
echo "Installing Python dependencies..."
source venv/bin/activate
pip install -r requirements.txt

cd ../..

# Create data directory
echo ""
echo "📁 Creating data directory..."
mkdir -p data

# Copy environment file if it doesn't exist
if [ ! -f ".env" ]; then
    if [ -f ".env.example" ]; then
        echo "📄 Copying .env.example to .env..."
        cp .env.example .env
        echo "⚠️  Please edit .env file with your configuration!"
    fi
fi

echo ""
echo "✅ Setup complete!"
echo ""
echo "🚀 To start the application:"
echo "   ./start.sh"
echo ""
echo "🔧 For development mode:"
echo "   yarn dev"
