#!/bin/bash

# Colors for terminal output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[CONSOLIDATE]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[!]${NC} $1"
}

print_error() {
    echo -e "${RED}[✗]${NC} $1"
}

# Function to create backup before making changes
create_backup() {
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local backup_dir="backups/consolidation_${timestamp}"
    
    print_status "Creating backup at $backup_dir..."
    mkdir -p "$backup_dir"
    
    # Backup directories that will be removed
    if [ -d "backend" ]; then
        cp -r backend "$backup_dir/" 2>/dev/null
        print_status "Backed up old TypeScript backend"
    fi
    
    if [ -d "frontend" ] && [ "$(ls -A frontend 2>/dev/null)" ]; then
        cp -r frontend "$backup_dir/" 2>/dev/null
        print_status "Backed up old frontend directory"
    fi
    
    # Backup configuration files that might be affected
    for file in package.json yarn.lock .env .env.example docker-compose*.yml; do
        if [ -f "$file" ]; then
            cp "$file" "$backup_dir/" 2>/dev/null
        fi
    done
    
    print_success "Backup created at $backup_dir"
    return 0
}

# Function to remove old backend
remove_old_backend() {
    print_status "Removing old TypeScript backend..."
    
    if [ -d "backend" ]; then
        # Check if it's the TypeScript backend
        if [ -f "backend/package.json" ] && grep -q "typescript" "backend/package.json" 2>/dev/null; then
            print_status "Found TypeScript backend at ./backend - removing..."
            rm -rf backend
            print_success "Removed old TypeScript backend directory"
        else
            print_warning "Backend directory exists but doesn't appear to be TypeScript - skipping"
        fi
    else
        print_status "No old backend directory found"
    fi
}

# Function to remove empty frontend directory
remove_empty_frontend() {
    print_status "Checking old frontend directory..."
    
    if [ -d "frontend" ]; then
        # Check if directory is effectively empty (only node_modules or cache files)
        local content_count=$(find frontend -type f -not -path "*/node_modules/*" -not -path "*/.cache/*" -not -name ".DS_Store" | wc -l)
        
        if [ "$content_count" -eq 0 ]; then
            print_status "Found empty frontend directory - removing..."
            rm -rf frontend
            print_success "Removed empty frontend directory"
        else
            print_warning "Frontend directory contains files - manual review required"
            echo "    Files found:"
            find frontend -type f -not -path "*/node_modules/*" -not -path "*/.cache/*" -not -name ".DS_Store" | head -5
        fi
    else
        print_status "No old frontend directory found"
    fi
}

# Function to update documentation references
update_documentation() {
    print_status "Updating documentation references..."
    
    # List of files to update
    local doc_files=(
        "README.md"
        "DEV_SETUP.md"
        "PROJECT_STATUS.md"
        "DASHBOARD_IMPLEMENTATION_SUMMARY.md"
        "docs/README.md"
        "documentation/PROJECT_OVERVIEW.md"
    )
    
    for file in "${doc_files[@]}"; do
        if [ -f "$file" ]; then
            print_status "Updating $file..."
            
            # Update backend references
            sed -i.bak 's|cd apps/backend|cd apps/backend|g' "$file" 2>/dev/null
            sed -i.bak 's|backend/|apps/backend/|g' "$file" 2>/dev/null
            sed -i.bak 's|backend-python|apps/backend|g' "$file" 2>/dev/null
            
            # Update frontend references
            sed -i.bak 's|cd apps/frontend|cd apps/frontend|g' "$file" 2>/dev/null
            sed -i.bak 's|frontend/|apps/frontend/|g' "$file" 2>/dev/null
            
            # Remove backup files
            rm -f "${file}.bak" 2>/dev/null
            
            print_status "Updated $file"
        fi
    done
    
    print_success "Documentation references updated"
}

# Function to update script references
update_scripts() {
    print_status "Updating script references..."
    
    # Find and update shell scripts
    find . -name "*.sh" -type f -not -path "./backups/*" -not -path "./.git/*" | while read -r script; do
        if grep -q "cd.*backend\|cd.*frontend" "$script" 2>/dev/null; then
            print_status "Updating script: $script"
            
            # Update backend references
            sed -i.bak 's|cd apps/backend|cd apps/backend|g' "$script" 2>/dev/null
            sed -i.bak 's|cd apps/frontend|cd apps/frontend|g' "$script" 2>/dev/null
            
            # Remove backup files
            rm -f "${script}.bak" 2>/dev/null
        fi
    done
    
    print_success "Script references updated"
}

# Function to clean up package.json files
clean_package_files() {
    print_status "Cleaning up package.json files..."
    
    # Remove root package.json if it's not needed
    if [ -f "package.json" ]; then
        # Check if it's a workspace configuration or has actual dependencies
        if grep -q "workspaces\|\"apps/\*\"" "package.json" 2>/dev/null; then
            print_status "Root package.json appears to be workspace config - keeping"
        elif ! grep -q "\"dependencies\":\s*{[^}]*[^}]" "package.json" 2>/dev/null; then
            print_warning "Root package.json might be redundant - manual review recommended"
        fi
    fi
    
    print_success "Package files cleaned"
}

# Function to update Docker configurations
update_docker_configs() {
    print_status "Updating Docker configurations..."
    
    # Find and update docker-compose files
    find . -name "docker-compose*.yml" -type f -not -path "./backups/*" | while read -r compose_file; do
        if grep -q "backend:\|frontend:" "$compose_file" 2>/dev/null; then
            print_status "Updating Docker compose: $compose_file"
            
            # Update context paths
            sed -i.bak 's|context: \./backend|context: ./apps/backend|g' "$compose_file" 2>/dev/null
            sed -i.bak 's|context: \./frontend|context: ./apps/frontend|g' "$compose_file" 2>/dev/null
            sed -i.bak 's|context: backend|context: apps/backend|g' "$compose_file" 2>/dev/null
            sed -i.bak 's|context: frontend|context: apps/frontend|g' "$compose_file" 2>/dev/null
            
            # Remove backup files
            rm -f "${compose_file}.bak" 2>/dev/null
        fi
    done
    
    print_success "Docker configurations updated"
}

# Function to verify consolidation
verify_consolidation() {
    print_status "Verifying project consolidation..."
    
    local issues=0
    
    # Check that apps structure exists
    if [ ! -d "apps/backend" ]; then
        print_error "apps/backend directory missing"
        issues=$((issues + 1))
    else
        print_success "apps/backend directory exists"
    fi
    
    if [ ! -d "apps/frontend" ]; then
        print_error "apps/frontend directory missing"
        issues=$((issues + 1))
    else
        print_success "apps/frontend directory exists"
    fi
    
    # Check that old directories are removed
    if [ -d "backend" ]; then
        print_warning "Old backend directory still exists"
        issues=$((issues + 1))
    else
        print_success "Old backend directory removed"
    fi
    
    if [ -d "frontend" ]; then
        print_warning "Old frontend directory still exists"
        issues=$((issues + 1))
    else
        print_success "Old frontend directory removed"
    fi
    
    # Check that key files exist in apps structure
    if [ ! -f "apps/backend/simple_main.py" ]; then
        print_error "Backend main file missing"
        issues=$((issues + 1))
    else
        print_success "Backend main file exists"
    fi
    
    if [ ! -f "apps/frontend/package.json" ]; then
        print_error "Frontend package.json missing"
        issues=$((issues + 1))
    else
        print_success "Frontend package.json exists"
    fi
    
    return $issues
}

# Function to update project workspace configuration
update_workspace_config() {
    print_status "Updating workspace configuration..."
    
    # Update VS Code workspace if it exists
    if [ -f "Mexel.code-workspace" ]; then
        print_status "Updating VS Code workspace configuration..."
        
        # Update folder paths to point to apps structure
        sed -i.bak 's|"path": "\./backend"|"path": "./apps/backend"|g' "Mexel.code-workspace" 2>/dev/null
        sed -i.bak 's|"path": "\./frontend"|"path": "./apps/frontend"|g' "Mexel.code-workspace" 2>/dev/null
        
        rm -f "Mexel.code-workspace.bak" 2>/dev/null
        print_success "VS Code workspace updated"
    fi
    
    # Update any yarn workspace configuration
    if [ -f "package.json" ] && grep -q "workspaces" "package.json" 2>/dev/null; then
        print_status "Updating yarn workspace configuration..."
        
        # Ensure workspaces point to apps/*
        if ! grep -q "apps/\*" "package.json" 2>/dev/null; then
            print_warning "Manual update needed for yarn workspaces in package.json"
        else
            print_success "Yarn workspaces already configured for apps structure"
        fi
    fi
    
    print_success "Workspace configuration updated"
}

# Main consolidation function
main() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}        MEXEL PROJECT CONSOLIDATION     ${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
    
    print_status "Starting project consolidation..."
    print_status "This will clean up the migrated project structure"
    echo ""
    
    # Confirm before proceeding
    print_warning "This will make changes to your project structure!"
    print_status "A backup will be created before any changes are made."
    echo ""
    read -p "Continue with consolidation? (y/N): " -n 1 -r
    echo ""
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Consolidation cancelled"
        exit 0
    fi
    
    echo ""
    print_status "Proceeding with consolidation..."
    echo ""
    
    # Create backup
    create_backup
    echo ""
    
    # Remove old structures
    print_status "=== Removing Old Structures ==="
    remove_old_backend
    remove_empty_frontend
    echo ""
    
    # Update references
    print_status "=== Updating References ==="
    update_documentation
    update_scripts
    update_docker_configs
    update_workspace_config
    echo ""
    
    # Clean up
    print_status "=== Cleaning Up ==="
    clean_package_files
    echo ""
    
    # Verify
    print_status "=== Verification ==="
    if verify_consolidation; then
        echo ""
        print_success "Project consolidation completed successfully!"
        echo ""
        echo -e "${GREEN}✓ Migrated to apps/ structure${NC}"
        echo -e "${GREEN}✓ Removed old directories${NC}"
        echo -e "${GREEN}✓ Updated all references${NC}"
        echo -e "${GREEN}✓ Created backup${NC}"
        echo ""
        print_status "Next steps:"
        echo "  1. Test the dashboard: ./start-dashboard.sh"
        echo "  2. Verify everything works: ./verify-dashboard.sh"
        echo "  3. Remove backup if satisfied: rm -rf backups/"
        echo ""
    else
        echo ""
        print_warning "Consolidation completed with some issues"
        print_status "Please review the warnings above and test your project"
        echo ""
    fi
}

# Run consolidation
main "$@"