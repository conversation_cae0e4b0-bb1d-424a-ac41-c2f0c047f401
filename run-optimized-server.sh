#!/bin/bash
# filepath: /Users/<USER>/Desktop/Mexel/run-optimized-server.sh

echo "Starting Mexel with optimized database architecture..."

# Check if docker-compose is running
if ! docker ps | grep -q mexel; then
  echo "Starting Docker containers..."
  docker-compose -f docker-compose.yml up -d
else
  echo "Docker containers are already running"
fi

# Wait for PostgreSQL to be ready
echo "Waiting for PostgreSQL to be ready..."
./scripts/wait-for-postgres.sh

# Apply database migrations if needed
echo "Applying database migrations..."
cd apps/backend-python && python -m alembic upgrade head && cd ..

# Start the backend server with improved connection pooling
echo "Starting backend server with optimized connection pooling..."
cd apps/backend-python && python run.py &
BACKEND_PID=$!

# Wait for backend to start
echo "Waiting for backend to initialize..."
sleep 5

# Start frontend with improved database architecture
echo "Starting frontend with optimized caching and data partitioning..."
cd apps/frontend && npm start &
FRONTEND_PID=$!

# Function to handle termination
function cleanup {
  echo "Shutting down servers..."
  kill $BACKEND_PID
  kill $FRONTEND_PID
  echo "Servers stopped"
}

# Register the cleanup function for when script is terminated
trap cleanup EXIT

# Keep the script running to maintain the servers
echo "Mexel server is running with optimized architecture. Press Ctrl+C to stop."
wait $FRONTEND_PID
