#!/bin/bash

# Run Dashboard Script
# This script starts both the frontend and backend servers for the Mexel dashboard

# Change to the project root directory
cd "$(dirname "$0")/.."

# Check if the frontend and backend directories exist
if [ ! -d "frontend" ]; then
  echo "Error: frontend directory not found"
  exit 1
fi

if [ ! -d "backend" ]; then
  echo "Error: backend directory not found"
  exit 1
fi

# Function to check if a port is in use
is_port_in_use() {
  lsof -i:"$1" > /dev/null 2>&1
  return $?
}

# Check if ports are already in use
if is_port_in_use 3000; then
  echo "Error: Port 3000 is already in use. Please stop any running frontend server."
  exit 1
fi

if is_port_in_use 3001; then
  echo "Error: Port 3001 is already in use. Please stop any running backend server."
  exit 1
fi

# Start the backend server
echo "Starting backend server..."
cd apps/backend
yarn start &
BACKEND_PID=$!
cd ..

# Wait for the backend to start
echo "Waiting for backend to start..."
sleep 5

# Start the frontend server
echo "Starting frontend server..."
cd apps/frontend
yarn start &
FRONTEND_PID=$!
cd ..

# Function to handle script termination
cleanup() {
  echo "Stopping servers..."
  kill $FRONTEND_PID $BACKEND_PID 2>/dev/null
  exit 0
}

# Set up trap to catch termination signals
trap cleanup SIGINT SIGTERM

echo "Dashboard is running!"
echo "Frontend: http://localhost:3000"
echo "Backend: http://localhost:3001"
echo "Press Ctrl+C to stop both servers"

# Keep the script running
wait $FRONTEND_PID $BACKEND_PID
