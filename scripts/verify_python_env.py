#!/usr/bin/env python3
"""
Python Environment Verification Script
This script checks the Python environment setup and dependencies for the Mexel project.
"""
import sys
import os
import pkg_resources
import importlib
import platform
import json
from pathlib import Path

def check_python_version():
    print("\n=== Python Version ===")
    print(f"Python Version: {sys.version}")
    print(f"Expected Version: 3.11.12")
    if not (sys.version_info.major == 3 and sys.version_info.minor == 11):
        print("⚠️  Warning: Python version doesn't match the expected version (3.11.x)")

def check_environment_variables():
    print("\n=== Environment Variables ===")
    important_vars = ['PYTHONPATH', 'VIRTUAL_ENV', 'PATH']
    for var in important_vars:
        value = os.environ.get(var, 'Not set')
        if var == 'PATH':
            paths = value.split(os.pathsep)
            print(f"\n{var}:")
            for path in paths:
                print(f"  - {path}")
        else:
            print(f"{var}: {value}")

def check_installed_packages():
    print("\n=== Installed Packages ===")
    required_packages = {
        'fastapi': None,
        'sqlalchemy': None,
        'alembic': None,
        'psycopg': None,
        'psycopg-binary': None,
        'pydantic': None,
    }
    
    installed_packages = {pkg.key: pkg.version for pkg in pkg_resources.working_set}
    
    for package in required_packages:
        version = installed_packages.get(package)
        if version:
            print(f"✓ {package}: {version}")
        else:
            print(f"✗ {package}: Not installed")

def test_imports():
    print("\n=== Import Tests ===")
    packages_to_test = [
        'fastapi',
        'sqlalchemy',
        'alembic',
        'psycopg',
        'pydantic',
    ]
    
    for package in packages_to_test:
        try:
            importlib.import_module(package)
            print(f"✓ Successfully imported {package}")
        except ImportError as e:
            print(f"✗ Failed to import {package}: {str(e)}")

def check_project_structure():
    print("\n=== Project Structure ===")
    project_root = Path(__file__).parent.parent
    backend_dir = project_root / 'backend-python'
    
    important_files = [
        backend_dir / 'requirements.txt',
        backend_dir / 'alembic.ini',
        backend_dir / 'app',
    ]
    
    for path in important_files:
        if path.exists():
            print(f"✓ Found {path.relative_to(project_root)}")
        else:
            print(f"✗ Missing {path.relative_to(project_root)}")

def check_vscode_settings():
    print("\n=== VS Code Settings ===")
    vscode_dir = Path(__file__).parent.parent / '.vscode'
    settings_file = vscode_dir / 'settings.json'
    
    if not settings_file.exists():
        print("✗ .vscode/settings.json not found")
        return
    
    try:
        with open(settings_file) as f:
            settings = json.load(f)
        
        python_settings = {k: v for k, v in settings.items() if k.startswith('python.')}
        print("Python-related VS Code settings:")
        for key, value in python_settings.items():
            print(f"  {key}: {value}")
    except Exception as e:
        print(f"✗ Error reading settings.json: {str(e)}")

def main():
    print("=== Mexel Python Environment Verification ===")
    print("Running environment checks...")
    
    check_python_version()
    check_environment_variables()
    check_installed_packages()
    test_imports()
    check_project_structure()
    check_vscode_settings()
    
    print("\nVerification complete!")

if __name__ == "__main__":
    main()
