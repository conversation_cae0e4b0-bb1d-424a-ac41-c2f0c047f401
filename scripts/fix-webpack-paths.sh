#!/bin/bash
echo "Fixing Webpack configuration paths..."
cd apps/frontend

# Find and replace absolute paths with relative paths
find . -type f \( -name "webpack*.js" -o -name "*.config.js" \) -exec sed -i '' "s|/Users/<USER>/Desktop/Mexel/frontend|.|g" {} \; 2>/dev/null || find . -type f \( -name "webpack*.js" -o -name "*.config.js" \) -exec sed -i "s|/Users/<USER>/Desktop/Mexel/frontend|.|g" {} \; 2>/dev/null
find . -type f \( -name "webpack*.js" -o -name "*.config.js" \) -exec sed -i '' "s|/Users/<USER>/Desktop/Mexel|..|g" {} \; 2>/dev/null || find . -type f \( -name "webpack*.js" -o -name "*.config.js" \) -exec sed -i "s|/Users/<USER>/Desktop/Mexel|..|g" {} \; 2>/dev/null

# Fix Node module resolution paths
find . -type f \( -name "webpack*.js" -o -name "*.config.js" \) -exec sed -i '' "s|frontend/node_modules|node_modules|g" {} \; 2>/dev/null || find . -type f \( -name "webpack*.js" -o -name "*.config.js" \) -exec sed -i "s|frontend/node_modules|node_modules|g" {} \; 2>/dev/null

# Fix HTML webpack plugin paths
find . -type f \( -name "webpack*.js" -o -name "*.config.js" \) -exec sed -i '' "s|require.resolve('html-webpack-plugin/lib/loader.js')|require.resolve(path.join(process.cwd(), 'node_modules/html-webpack-plugin/lib/loader.js'))|g" {} \; 2>/dev/null || find . -type f \( -name "webpack*.js" -o -name "*.config.js" \) -exec sed -i "s|require.resolve('html-webpack-plugin/lib/loader.js')|require.resolve(path.join(process.cwd(), 'node_modules/html-webpack-plugin/lib/loader.js'))|g" {} \; 2>/dev/null

echo "✅ Webpack paths fixed"
cd ../..