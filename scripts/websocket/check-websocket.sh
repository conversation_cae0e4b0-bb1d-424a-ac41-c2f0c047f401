#!/bin/zsh

# Colors for terminal output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Starting WebSocket Status Check...${NC}"

# Check if backend is running
echo -e "${YELLOW}Checking backend status...${NC}"
if ! curl -s http://localhost:3001/health > /dev/null; then
    echo -e "${RED}Backend server does not appear to be running. Start it first.${NC}"
    exit 1
fi

echo -e "${GREEN}Backend is running.${NC}"

# Check WebSocket API status
echo -e "${YELLOW}Checking WebSocket API status...${NC}"
WEBSOCKET_STATUS=$(curl -s http://localhost:3001/api/websocket/status)

if [ $? -eq 0 ]; then
    echo -e "${GREEN}WebSocket API is available:${NC}"
    echo $WEBSOCKET_STATUS | grep . || echo "Empty response"
    
    # Use grep and awk instead of Python for better compatibility
    CLIENT_COUNT=$(echo $WEBSOCKET_STATUS | grep -o '"clientCount":[0-9]*' | awk -F ':' '{print $2}' || echo "0")
    
    echo -e "\n${YELLOW}Currently connected clients:${NC} $CLIENT_COUNT"
    
    # Get detailed client information if there are any
    if [ "$CLIENT_COUNT" -gt 0 ]; then
        echo -e "\n${YELLOW}Client details:${NC}"
        curl -s http://localhost:3001/api/websocket/clients | python -m json.tool
    fi
else
    echo -e "${RED}WebSocket API check failed. Verify that the endpoint is correctly implemented.${NC}"
    exit 1
fi

echo -e "\n${GREEN}WebSocket status check complete.${NC}"
echo -e "${YELLOW}To test interactive WebSocket connection, run:${NC} ./test-websocket.sh"

exit 0
