#!/bin/zsh

# Colors for terminal output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Load shared configuration if available
CONFIG_FILE="$(dirname "$0")/websocket.config"
if [ -f "$CONFIG_FILE" ]; then
  source "$CONFIG_FILE"
fi

# Default settings (can be overridden by websocket.config)
MONITOR_INTERVAL=${MONITOR_INTERVAL:-5}  # seconds between checks
API_URL=${API_URL:-"http://localhost:3001"}
WS_URL=${WS_URL:-"http://localhost:3001"}
MONITOR_MODE=${MONITOR_MODE:-"brief"}  # brief or detailed
LOG_PATH=${LOG_PATH:-"/tmp/mexel-ws-monitor.log"}
MAX_HISTORY=${MAX_HISTORY:-10}        # Keep last 10 checks in memory

# Error handling function
handle_error() {
  local error_msg="$1"
  local error_code="${2:-1}"
  echo -e "${RED}Error: ${error_msg}${NC}" >&2
  [ -n "$LOG_PATH" ] && echo "[ERROR] $(date '+%Y-%m-%d %H:%M:%S') - ${error_msg}" >> "$LOG_PATH"
  return $error_code
}

# Success logging function
log_success() {
  local msg="$1"
  echo -e "${GREEN}${msg}${NC}"
  [ -n "$LOG_PATH" ] && echo "[SUCCESS] $(date '+%Y-%m-%d %H:%M:%S') - ${msg}" >> "$LOG_PATH"
}

# Verify dependencies are installed
check_dependencies() {
  local missing_deps=()
  
  if ! command -v curl &> /dev/null; then
    missing_deps+=("curl")
  fi
  
  if ! command -v jq &> /dev/null; then
    missing_deps+=("jq")
  fi
  
  if [ ${#missing_deps[@]} -ne 0 ]; then
    handle_error "Missing required dependencies: ${missing_deps[*]}"
    echo -e "${YELLOW}Please install missing dependencies:${NC}"
    echo "brew install ${missing_deps[*]}"
    return 1
  fi
  
  return 0
}

# Initialize log file
init_log() {
  if [ -n "$LOG_PATH" ]; then
    # Ensure log directory exists
    local log_dir=$(dirname "$LOG_PATH")
    if [ ! -d "$log_dir" ]; then
      mkdir -p "$log_dir" || handle_error "Could not create log directory: $log_dir"
    fi
    
    # Initialize or rotate log file
    if [ -f "$LOG_PATH" ] && [ $(stat -f%z "$LOG_PATH") -gt 1048576 ]; then
      mv "$LOG_PATH" "${LOG_PATH}.old"
    fi
    
    touch "$LOG_PATH" || handle_error "Could not create/access log file: $LOG_PATH"
    echo "[INFO] $(date '+%Y-%m-%d %H:%M:%S') - WebSocket monitoring started" >> "$LOG_PATH"
  fi
}

# Print usage information
print_usage() {
  echo "WebSocket Monitor for Mexel"
  echo ""
  echo "Usage: $0 [options]"
  echo ""
  echo "Options:"
  echo "  -i, --interval SECONDS   Set monitoring interval (default: 5s)"
  echo "  -a, --api URL            API URL (default: http://localhost:3001)"
  echo "  -w, --ws URL             WebSocket URL (default: http://localhost:3001)"
  echo "  -m, --mode MODE          Monitor mode: brief or detailed (default: brief)"
  echo "  -l, --log FILE           Log file path (default: /tmp/mexel-ws-monitor.log)"
  echo "  -h, --help               Show this help message"
  echo ""
  echo "Example:"
  echo "  $0 --interval 10 --mode detailed --log websocket-status.log"
  exit 1
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  key="$1"
  case $key in
    -i|--interval)
      MONITOR_INTERVAL="$2"
      shift 2
      ;;
    -a|--api)
      API_URL="$2"
      shift 2
      ;;
    -w|--ws)
      WS_URL="$2"
      shift 2
      ;;
    -m|--mode)
      MONITOR_MODE="$2"
      shift 2
      ;;
    -l|--log)
      LOG_PATH="$2"
      shift 2
      ;;
    -h|--help)
      print_usage
      ;;
    *)
      echo -e "${RED}Unknown option: $1${NC}" >&2
      print_usage
      ;;
  esac
done

# Initialize history arrays
client_count_history=()
ws_status_history=()
api_response_time_history=()

# Check for dependencies
echo -e "${YELLOW}Checking dependencies...${NC}"
DEPENDENCIES_MET=true

# Check for curl
if ! command -v curl &> /dev/null; then
  echo -e "${RED}curl is required but not installed.${NC}"
  DEPENDENCIES_MET=false
fi

# Check for jq
JQ_AVAILABLE=false
if command -v jq &> /dev/null; then
  JQ_AVAILABLE=true
  echo -e "${GREEN}Using jq for JSON processing${NC}"
else
  echo -e "${YELLOW}jq is not installed. JSON output will not be formatted.${NC}"
  echo -e "${YELLOW}Install jq for better output: brew install jq${NC}"
fi

# Python for advanced functions
PYTHON_AVAILABLE=false
if command -v python3 &> /dev/null; then
  PYTHON_AVAILABLE=true
  PYTHON_CMD="python3"
  echo -e "${GREEN}Using Python for advanced features${NC}"
elif command -v python &> /dev/null; then
  PYTHON_AVAILABLE=true
  PYTHON_CMD="python"
  echo -e "${GREEN}Using Python for advanced features${NC}"
else
  echo -e "${YELLOW}Python is not available. Some advanced features will be disabled.${NC}"
fi

if [ "$DEPENDENCIES_MET" = false ]; then
  echo -e "${RED}Please install the required dependencies and try again.${NC}"
  exit 1
fi

# Initialize log
init_log

# Function to check API health
check_api_health() {
  echo -e "${YELLOW}Checking API health...${NC}"
  start_time=$(date +%s.%N)
  response=$(curl -s -o /dev/null -w "%{http_code}" "${API_URL}/health")
  end_time=$(date +%s.%N)
  response_time=$(echo "$end_time - $start_time" | bc)
  
  if [ "$response" = "200" ]; then
    echo -e "${GREEN}API is healthy (HTTP 200)${NC}"
    echo -e "${BLUE}Response time: ${response_time}s${NC}"
    api_status="UP"
    api_response_time_history+=($response_time)
    
    # Keep history at max length
    if [ ${#api_response_time_history[@]} -gt $MAX_HISTORY ]; then
      api_response_time_history=("${api_response_time_history[@]:1}")
    fi
  else
    echo -e "${RED}API returned HTTP ${response}${NC}"
    api_status="DOWN"
  fi
}

# Function to check WebSocket status
check_websocket_status() {
  echo -e "${YELLOW}Checking WebSocket status...${NC}"
  ws_status_response=$(curl -s "${API_URL}/api/websocket/status")
  
  if [ -z "$ws_status_response" ]; then
    echo -e "${RED}WebSocket status endpoint is not responding${NC}"
    ws_status="DOWN"
    ws_status_history+=("DOWN")
    client_count_history+=(0)
    return
  fi
  
  # Parse the JSON response
  if [ "$JQ_AVAILABLE" = true ]; then
    ws_status=$(echo $ws_status_response | jq -r '.status')
    client_count=$(echo $ws_status_response | jq -r '.connected_clients')
    socket_io_version=$(echo $ws_status_response | jq -r '.server_info.socket_io_version')
    asgi_mode=$(echo $ws_status_response | jq -r '.server_info.asgi_mode')
    
    echo -e "${BLUE}WebSocket status: ${ws_status}${NC}"
    echo -e "${BLUE}Connected clients: ${client_count}${NC}"
    echo -e "${BLUE}Socket.IO version: ${socket_io_version}${NC}"
    echo -e "${BLUE}ASGI mode: ${asgi_mode}${NC}"
    
    echo $ws_status_response | jq .
  elif [ "$PYTHON_AVAILABLE" = true ]; then
    ws_status=$($PYTHON_CMD -c "import sys, json; print(json.loads('''$ws_status_response''').get('status', 'unknown'))")
    client_count=$($PYTHON_CMD -c "import sys, json; print(json.loads('''$ws_status_response''').get('connected_clients', 0))")
    socket_io_version=$($PYTHON_CMD -c "import sys, json; print(json.loads('''$ws_status_response''').get('server_info', {}).get('socket_io_version', 'unknown'))")
    asgi_mode=$($PYTHON_CMD -c "import sys, json; print(json.loads('''$ws_status_response''').get('server_info', {}).get('asgi_mode', 'unknown'))")
    
    echo -e "${BLUE}WebSocket status: ${ws_status}${NC}"
    echo -e "${BLUE}Connected clients: ${client_count}${NC}"
    echo -e "${BLUE}Socket.IO version: ${socket_io_version}${NC}"
    echo -e "${BLUE}ASGI mode: ${asgi_mode}${NC}"
    
    echo $ws_status_response | $PYTHON_CMD -m json.tool
  else
    echo -e "${YELLOW}Raw WebSocket status response:${NC}"
    echo $ws_status_response
    
    # Try to extract client count using grep
    client_count=$(echo $ws_status_response | grep -o '"connected_clients":[0-9]*' | grep -o '[0-9]*')
    if [ -z "$client_count" ]; then
      client_count=0
    fi
    
    # Try to extract status using grep
    ws_status=$(echo $ws_status_response | grep -o '"status":"[^"]*"' | grep -o ':"[^"]*"' | sed 's/:"//g' | sed 's/"//g')
    if [ -z "$ws_status" ]; then
      ws_status="unknown"
    fi
  fi
  
  # Update history
  ws_status_history+=("$ws_status")
  client_count_history+=($client_count)
  
  # Keep history at max length
  if [ ${#ws_status_history[@]} -gt $MAX_HISTORY ]; then
    ws_status_history=("${ws_status_history[@]:1}")
    client_count_history=("${client_count_history[@]:1}")
  fi
  
  # If detailed mode and there are clients, get client details
  if [ "$MONITOR_MODE" = "detailed" ] && [ "$client_count" -gt 0 ]; then
    echo -e "${YELLOW}Getting client details...${NC}"
    client_details=$(curl -s "${API_URL}/api/websocket/clients")
    
    if [ -n "$client_details" ]; then
      echo -e "${BLUE}Client details:${NC}"
      if [ "$JQ_AVAILABLE" = true ]; then
        echo $client_details | jq .
      elif [ "$PYTHON_AVAILABLE" = true ]; then
        echo $client_details | $PYTHON_CMD -m json.tool
      else
        echo $client_details
      fi
    fi
  fi
}

# Function to display history
display_history() {
  echo -e "\n${YELLOW}History (last $MAX_HISTORY checks):${NC}"
  
  if [ ${#client_count_history[@]} -eq 0 ]; then
    echo "No history yet"
    return
  fi
  
  echo -e "${BLUE}Client count history:${NC}"
  history_length=${#client_count_history[@]}
  
  # Create a simple ASCII chart
  if [ "$PYTHON_AVAILABLE" = true ] && [ $history_length -gt 1 ]; then
    max_count=$(echo "${client_count_history[@]}" | tr ' ' '\n' | sort -nr | head -n1)
    if [ $max_count -eq 0 ]; then max_count=1; fi
    
    $PYTHON_CMD -c "
import sys
history = [int(x) for x in '${client_count_history[@]}'.split()]
max_val = max(history) if max(history) > 0 else 1
height = 5
print('Chart (max clients: ' + str(max_val) + ')')
for h in range(height, 0, -1):
    line = ''
    for val in history:
        normalized = int((val / max_val) * height)
        if normalized >= h:
            line += '█ '
        else:
            line += '  '
    print(line)
print(''.join(['--' for _ in range(len(history))]))
times = range(len(history))
time_markers = [''.join([str(i).ljust(2) for i in range(len(history))])]
print(time_markers[0])
"
  else
    for i in "${!client_count_history[@]}"; do
      echo "Check $((i+1)): ${client_count_history[$i]} clients (${ws_status_history[$i]})"
    done
  fi
}

# Function to log results
log_results() {
  timestamp=$(date +"%Y-%m-%d %H:%M:%S")
  echo "$timestamp | API: $api_status | WebSocket: ${ws_status:-unknown} | Clients: ${client_count:-0}" >> "$LOG_PATH"
}

# Main monitoring loop
echo -e "${GREEN}Starting WebSocket monitor for Mexel${NC}"
echo -e "${BLUE}API URL: ${API_URL}${NC}"
echo -e "${BLUE}WebSocket URL: ${WS_URL}${NC}"
echo -e "${BLUE}Monitor interval: ${MONITOR_INTERVAL}s${NC}"
echo -e "${BLUE}Monitor mode: ${MONITOR_MODE}${NC}"
echo -e "${BLUE}Log file: ${LOG_PATH}${NC}"
echo -e "${YELLOW}Press Ctrl+C to stop monitoring${NC}\n"

# Clear the log file
echo "# Mexel WebSocket Monitor Log" > "$LOG_PATH"
echo "# Started at: $(date)" >> "$LOG_PATH"
echo "# API URL: ${API_URL}" >> "$LOG_PATH"
echo "# WebSocket URL: ${WS_URL}" >> "$LOG_PATH"
echo "# Format: timestamp | API status | WebSocket status | Client count" >> "$LOG_PATH"
echo "------------------------------------------------------------" >> "$LOG_PATH"

try_count=0
while true; do
  clear
  echo -e "${CYAN}=== Mexel WebSocket Monitor ===${NC}"
  echo -e "${CYAN}Check #$((try_count + 1)) at $(date "+%Y-%m-%d %H:%M:%S")${NC}"
  echo -e "${CYAN}===============================${NC}\n"
  
  # Run the checks
  check_api_health
  check_websocket_status
  
  # Display history if we have more than one data point
  if [ ${#client_count_history[@]} -gt 1 ]; then
    display_history
  fi
  
  # Log results
  log_results
  
  # Increment counter
  try_count=$((try_count + 1))
  
  echo -e "\n${YELLOW}Monitoring... next check in ${MONITOR_INTERVAL}s (Ctrl+C to stop)${NC}"
  sleep $MONITOR_INTERVAL
done
