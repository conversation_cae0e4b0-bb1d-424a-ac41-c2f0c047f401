#!/bin/zsh

# Script to install required dependencies for WebSocket testing tools

# Colors for terminal output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Error handling
set -e
trap 'echo -e "${RED}Error: Setup failed${NC}" >&2; exit 1' ERR

echo -e "${YELLOW}Setting up WebSocket testing dependencies...${NC}"

# Check for Node.js and npm
if ! command -v node &> /dev/null; then
    echo -e "${YELLOW}Installing Node.js...${NC}"
    brew install node
fi

# Check for required system tools
SYSTEM_DEPS=(curl jq)
MISSING_DEPS=()

for dep in "${SYSTEM_DEPS[@]}"; do
    if ! command -v "$dep" &> /dev/null; then
        MISSING_DEPS+=("$dep")
    fi
done

if [ ${#MISSING_DEPS[@]} -ne 0 ]; then
    echo -e "${YELLOW}Installing required system tools: ${MISSING_DEPS[*]}${NC}"
    brew install "${MISSING_DEPS[@]}"
fi

# Install Node.js dependencies
echo -e "${YELLOW}Installing Node.js dependencies...${NC}"
cd "$(dirname "$0")"
npm install

echo -e "${GREEN}✓ WebSocket testing dependencies installed successfully!${NC}"
echo -e "\nYou can now use the WebSocket testing tools:"
echo -e "  ${YELLOW}./websocket-tool.sh test${NC}     # Run interactive tests"
echo -e "  ${YELLOW}./websocket-tool.sh monitor${NC}  # Monitor connections"
echo -e "  ${YELLOW}./websocket-tool.sh status${NC}   # Check server status"
