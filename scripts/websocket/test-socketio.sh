#!/bin/zsh

# Colors for terminal output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Get WebSocket URL from argument or use default
SOCKETIO_URL=${1:-"http://localhost:3001"}

echo -e "${YELLOW}Testing Socket.IO connection to:${NC} $SOCKETIO_URL"

# Function to test Socket.IO connection with Node.js
test_with_node() {
    echo -e "${YELLOW}Using Node.js for Socket.IO testing${NC}"
    echo -e "${YELLOW}Press Ctrl+C to exit${NC}"
    
    # Create a temporary directory and install socket.io-client
    TMP_DIR=$(mktemp -d /tmp/socketio-test-XXXXXX)
    cd $TMP_DIR
    npm init -y > /dev/null 2>&1
    echo -e "${YELLOW}Installing socket.io-client...${NC}"
    npm install socket.io-client > /dev/null 2>&1
    
    # Create a temporary file with Node.js Socket.IO code
    TMP_FILE="$TMP_DIR/socketio-test.js"
    
    cat > $TMP_FILE << 'EOF'
const { io } = require("socket.io-client");
const readline = require("readline");

const url = process.argv[2] || 'http://localhost:3001';
const socket = io(url, {
  transports: ['websocket', 'polling'],
  reconnectionAttempts: 5,
  reconnectionDelay: 1000
});

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Connection events
socket.on('connect', () => {
  console.log('\x1b[32mConnected to Socket.IO server!\x1b[0m');
  console.log(`Socket ID: ${socket.id}`);
  console.log('Available events to test:');
  console.log('  - ping: Send a ping to the server');
  console.log('  - echo [message]: Echo a message back');
  console.log('  - join [room]: Join a room');
  console.log('  - leave [room]: Leave a room');
  console.log('  - room [room] [message]: Send message to a room');
  console.log('Type a command and press Enter. Type "exit" to quit.');
  
  rl.on('line', (input) => {
    if (input.toLowerCase() === 'exit') {
      console.log('Disconnecting...');
      socket.disconnect();
      rl.close();
      process.exit(0);
    } else if (input.toLowerCase() === 'ping') {
      socket.emit('ping');
      console.log('\x1b[33mSent ping request\x1b[0m');
    } else if (input.toLowerCase().startsWith('echo ')) {
      const message = input.substring(5);
      socket.emit('echo', message);
      console.log(`\x1b[33mSent echo request: ${message}\x1b[0m`);
    } else if (input.toLowerCase().startsWith('join ')) {
      const room = input.substring(5);
      socket.emit('join_room', room);
      console.log(`\x1b[33mRequested to join room: ${room}\x1b[0m`);
    } else if (input.toLowerCase().startsWith('leave ')) {
      const room = input.substring(6);
      socket.emit('leave_room', room);
      console.log(`\x1b[33mRequested to leave room: ${room}\x1b[0m`);
    } else if (input.toLowerCase().startsWith('room ')) {
      const parts = input.substring(5).split(' ');
      if (parts.length >= 2) {
        const room = parts[0];
        const message = parts.slice(1).join(' ');
        socket.emit('room_message', { room, message });
        console.log(`\x1b[33mSent message to room ${room}: ${message}\x1b[0m`);
      } else {
        console.log('\x1b[31mInvalid format. Use: room [room_name] [message]\x1b[0m');
      }
    } else {
      socket.emit('message', input);
      console.log(`\x1b[33mSent generic message: ${input}\x1b[0m`);
    }
  });
});

// Event handlers for server responses
socket.on('welcome', (data) => {
  console.log(`\x1b[36mWelcome message: ${data.message}\x1b[0m`);
});

socket.on('response', (data) => {
  console.log(`\x1b[36mServer response: ${JSON.stringify(data)}\x1b[0m`);
});

socket.on('echo_response', (data) => {
  console.log(`\x1b[36mEcho response: ${JSON.stringify(data)}\x1b[0m`);
});

socket.on('pong', (data) => {
  console.log(`\x1b[36mPong received at ${data.timestamp}: ${data.message}\x1b[0m`);
});

socket.on('room_joined', (data) => {
  console.log(`\x1b[36mJoined room: ${data.room}\x1b[0m`);
});

socket.on('room_left', (data) => {
  console.log(`\x1b[36mLeft room: ${data.room}\x1b[0m`);
});

socket.on('room_message', (data) => {
  console.log(`\x1b[36mRoom message from ${data.sender} at ${data.timestamp}: ${data.message}\x1b[0m`);
});

socket.on('user_joined', (data) => {
  console.log(`\x1b[36mUser ${data.sid} joined the room\x1b[0m`);
});

socket.on('user_left', (data) => {
  console.log(`\x1b[36mUser ${data.sid} left the room\x1b[0m`);
});

socket.on('disconnect', () => {
  console.log('\x1b[31mDisconnected from Socket.IO server\x1b[0m');
});

socket.on('connect_error', (err) => {
  console.log(`\x1b[31mConnection error: ${err.message}\x1b[0m`);
});
EOF

    # Run the Node.js script
    node $TMP_FILE $SOCKETIO_URL
    
    # Clean up the temporary directory and all its contents
    cd - > /dev/null
    rm -rf $TMP_DIR
}

# Check if Node.js is available
if command -v node &> /dev/null; then
    test_with_node
else
    echo -e "${RED}Node.js is required for Socket.IO testing.${NC}"
    echo -e "${YELLOW}Please install Node.js to use this script.${NC}"
    echo -e "Visit https://nodejs.org/ for installation instructions."
    exit 1
fi

exit 0
