#!/bin/zsh

# Colors for terminal output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Starting WebSocket Status Check...${NC}"

# Find available Python interpreter (prefer Python 3)
PYTHON_CMD=""
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
else
    echo -e "${RED}No Python interpreter found. Using basic tools instead.${NC}"
    PYTHON_CMD=""
fi

if [ -n "$PYTHON_CMD" ]; then
    echo -e "${GREEN}Using Python interpreter:${NC} $(${PYTHON_CMD} --version)"
fi

# Check if jq is installed (alternative JSON processor)
JQ_AVAILABLE=false
if command -v jq &> /dev/null; then
    JQ_AVAILABLE=true
    echo -e "${GREEN}Using jq for JSON processing${NC}"
fi

# Check if backend is running
echo -e "${YELLOW}Checking backend status...${NC}"
if ! curl -s http://localhost:3001/health > /dev/null; then
    echo -e "${RED}Backend server does not appear to be running. Start it first.${NC}"
    exit 1
fi

echo -e "${GREEN}Backend is running.${NC}"

# Check WebSocket API status
echo -e "${YELLOW}Checking WebSocket API status...${NC}"
WEBSOCKET_STATUS=$(curl -s http://localhost:3001/api/websocket/status)

if [ $? -eq 0 ]; then
    echo -e "${GREEN}WebSocket API is available:${NC}"
    
    # Process JSON with available tools
    if [ "$JQ_AVAILABLE" = true ]; then
        echo $WEBSOCKET_STATUS | jq .
        # Extract client count
        CLIENT_COUNT=$(echo $WEBSOCKET_STATUS | jq '.connected_clients')
    elif [ -n "$PYTHON_CMD" ]; then
        echo $WEBSOCKET_STATUS | $PYTHON_CMD -m json.tool
        # Extract client count using Python
        CLIENT_COUNT=$(echo $WEBSOCKET_STATUS | $PYTHON_CMD -c "import sys, json; print(json.load(sys.stdin).get('connected_clients', 0))")
    else
        # Fallback to basic tools
        echo $WEBSOCKET_STATUS
        CLIENT_COUNT=$(echo $WEBSOCKET_STATUS | grep -o '"connected_clients":[0-9]*' | awk -F: '{print $2}')
    fi
    
    # Check if CLIENT_COUNT is empty or null
    if [ -z "$CLIENT_COUNT" ] || [ "$CLIENT_COUNT" = "null" ]; then
        CLIENT_COUNT=0
        echo -e "\n${YELLOW}Warning: Could not determine client count, assuming 0.${NC}"
    fi
    
    echo -e "\n${YELLOW}Currently connected clients:${NC} $CLIENT_COUNT"
    
    # Get detailed client information if there are any
    if [ "$CLIENT_COUNT" -gt 0 ]; then
        echo -e "\n${YELLOW}Client details:${NC}"
        CLIENT_DETAILS=$(curl -s http://localhost:3001/api/websocket/clients)
        
        if [ "$JQ_AVAILABLE" = true ]; then
            echo $CLIENT_DETAILS | jq .
        elif [ -n "$PYTHON_CMD" ]; then
            echo $CLIENT_DETAILS | $PYTHON_CMD -m json.tool
        else
            echo $CLIENT_DETAILS
        fi
    fi
else
    echo -e "${RED}WebSocket API check failed. Verify that the endpoint is correctly implemented.${NC}"
    exit 1
fi

echo -e "\n${GREEN}WebSocket status check complete.${NC}"
echo -e "${YELLOW}To test interactive WebSocket connection, run:${NC} ./test-websocket.sh"

exit 0
