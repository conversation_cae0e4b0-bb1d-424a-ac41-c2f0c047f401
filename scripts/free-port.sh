#!/bin/bash

# Script to free up ports by killing processes using them

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to check if a port is in use
check_port() {
  local port=$1
  if lsof -i :$port >/dev/null 2>&1; then
    return 0 # Port is in use (success)
  else
    return 1 # Port is not in use (failure)
  fi
}

# Function to free up a port
free_port() {
  local port=$1
  
  echo -e "${BLUE}Checking port $port...${NC}"
  
  if check_port $port; then
    echo -e "${YELLOW}Port $port is in use. Attempting to free it...${NC}"
    
    # Get PIDs using the port
    local pids=$(lsof -t -i :$port 2>/dev/null)
    
    if [ ! -z "$pids" ]; then
      echo -e "${YELLOW}Killing processes: $pids${NC}"
      kill -9 $pids 2>/dev/null
      sleep 2
      
      # Verify port is now free
      if check_port $port; then
        echo -e "${RED}Failed to free port $port!${NC}"
        return 1
      else
        echo -e "${GREEN}Successfully freed port $port${NC}"
        return 0
      fi
    fi
  else
    echo -e "${GREEN}Port $port is already available${NC}"
    return 0
  fi
}

# Main execution
if [ $# -eq 0 ]; then
  echo "Usage: $0 <port_number> [<port_number2> ...]"
  exit 1
fi

# Process all port arguments
for port in "$@"; do
  if [[ "$port" =~ ^[0-9]+$ ]]; then
    free_port $port
  else
    echo -e "${RED}Invalid port number: $port${NC}"
  fi
done

exit 0