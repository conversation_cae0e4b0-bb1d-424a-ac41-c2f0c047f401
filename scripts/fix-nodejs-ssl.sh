#!/bin/bash

# Node.js SSL/TLS Configuration Fix Script
# This script implements multiple strategies to fix Node.js SSL/TLS handshake failures

echo "🔧 Applying Node.js SSL/TLS Fixes..."

# 1. Set Node.js SSL/TLS Environment Variables
export NODE_TLS_REJECT_UNAUTHORIZED=0
export SSL_REJECT_UNAUTHORIZED=0
export NODE_OPTIONS="--tls-min-v1.2 --tls-max-v1.3 --max-old-space-size=4096"

# 2. Fix OpenSSL legacy issues (Node.js 17+)
if node --version | grep -q "v1[7-9]\|v[2-9][0-9]"; then
    echo "📋 Detected Node.js 17+ - applying OpenSSL legacy provider fix..."
    export NODE_OPTIONS="$NODE_OPTIONS --openssl-legacy-provider"
fi

# 3. Configure SSL certificate paths
export SSL_CERT_PATH="./backend/certs/server.cert"
export SSL_KEY_PATH="./backend/certs/server.key"
export SSL_CA_PATH="./backend/certs/ca.crt"

# 4. Set secure cipher suites (CHACHA20-POLY1305 is a valid cryptographic cipher suite name)
export SSL_CIPHER_SUITES="ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305"

# 5. Create Node.js SSL configuration file
cat > node-ssl-config.js << 'EOF'
// Node.js SSL/TLS Configuration
const https = require('https');
const tls = require('tls');

// Configure default HTTPS agent
https.globalAgent.options.rejectUnauthorized = false;
https.globalAgent.options.secureProtocol = 'TLSv1_2_method';

// Configure TLS options
const originalCreateSecureContext = tls.createSecureContext;
tls.createSecureContext = function(options = {}) {
    return originalCreateSecureContext({
        ...options,
        minVersion: 'TLSv1.2',
        maxVersion: 'TLSv1.3',
        ciphers: process.env.SSL_CIPHER_SUITES || 'ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256',
        honorCipherOrder: true,
        secureOptions: require('constants').SSL_OP_NO_SSLv2 | require('constants').SSL_OP_NO_SSLv3
    });
};

// Configure process-wide TLS settings
process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';

console.log('✅ Node.js SSL/TLS configuration applied');
EOF

echo "✅ Node.js SSL/TLS fixes applied successfully!"
echo ""
echo "🚀 Usage:"
echo "   1. Run: source scripts/fix-nodejs-ssl.sh"
echo "   2. Or: npm start (configuration will be auto-loaded)"
echo ""
echo "🔍 Environment Variables Set:"
echo "   NODE_TLS_REJECT_UNAUTHORIZED: $NODE_TLS_REJECT_UNAUTHORIZED"
echo "   NODE_OPTIONS: $NODE_OPTIONS"
echo "   SSL_CERT_PATH: $SSL_CERT_PATH"
echo ""
