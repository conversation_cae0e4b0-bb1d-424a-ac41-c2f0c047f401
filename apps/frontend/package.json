{"name": "@mexel/frontend", "version": "1.0.0", "private": true, "scripts": {"dev": "react-scripts start", "start": "react-scripts start", "build": "react-scripts build", "test": "jest --config jest.config.js", "test:watch": "jest --watch --config jest.config.js", "test:ci": "jest --ci --coverage --config jest.config.js", "test:e2e": "yarn playwright test", "test:e2e:install": "yarn playwright install", "eject": "react-scripts eject", "lint": "eslint . --ext .ts,.tsx", "lint:fix": "eslint . --ext .ts,.tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,json,md}\""}, "dependencies": {"@babel/runtime": "^7.24.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.1", "@mui/material": "^7.1.1", "@mui/system": "5.15.15", "@mui/x-data-grid": "6.19.8", "@mui/x-date-pickers": "6.19.5", "@testing-library/dom": "9.3.3", "axios": "1.6.7", "date-fns": "3.6.0", "lodash": "4.17.21", "react": "18.2.0", "react-dom": "18.2.0", "react-markdown": "10.1.0", "react-router-dom": "6.22.1", "react-scripts": "5.0.1", "recharts": "2.15.3", "socket.io-client": "4.8.1", "typescript": "4.9.5", "uuid": "11.1.0", "zustand": "5.0.5"}, "devDependencies": {"@babel/core": "^7.24.0", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/plugin-transform-runtime": "^7.24.0", "@babel/preset-env": "^7.24.0", "@babel/preset-react": "^7.23.3", "@babel/preset-typescript": "^7.23.3", "@playwright/test": "1.52.0", "@testing-library/jest-dom": "6.6.3", "@testing-library/react": "16.3.0", "@testing-library/user-event": "14.6.1", "@types/jest": "29.5.14", "@types/lodash": "4.17.17", "@types/node": "18.19.14", "@types/react": "18.2.0", "@types/react-dom": "18.2.0", "@types/socket.io-client": "3.0.0", "@types/uuid": "10.0.0", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "babel-plugin-transform-react-remove-prop-types": "^0.4.24", "eslint": "^8.57.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "jest": "29.7.0", "jest-environment-jsdom": "30.0.0-beta.3", "playwright": "1.52.0", "prettier": "^3.5.3", "react-refresh": "^0.14.0", "ts-jest": "29.3.4", "ts-node": "10.9.2", "typescript": "4.9.5"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}}