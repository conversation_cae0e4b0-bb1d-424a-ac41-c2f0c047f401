#!/usr/bin/env python3
"""
Comprehensive test for VS Code IntelliSense and Python features in Mexel workspace
"""

import sys
import os
from pathlib import Path
from typing import List, Dict, Optional
from dataclasses import dataclass
from universal_deepseek import ask, test

@dataclass
class TestResult:
    """Data class to test IntelliSense with type hints."""
    name: str
    success: bool
    message: Optional[str] = None

def test_basic_python() -> TestResult:
    """Test basic Python features and IntelliSense."""
    try:
        # Test list comprehensions
        numbers: List[int] = [1, 2, 3, 4, 5]
        squared: List[int] = [n**2 for n in numbers]

        # Test dictionary operations
        data: Dict[str, int] = {"a": 1, "b": 2, "c": 3}
        filtered_data = {k: v for k, v in data.items() if v > 1}

        # Test path operations with type hints
        current_path: Path = Path.cwd()
        parent_path: Path = current_path.parent

        return TestResult("basic_python", True, f"Squared: {squared}, Filtered: {filtered_data}")
    except Exception as e:
        return TestResult("basic_python", False, str(e))

def test_deepseek_integration() -> TestResult:
    """Test DeepSeek integration functions."""
    try:
        # Test the ask function
        response = ask("What is 2+2? Answer in one word.")

        # Test the test function
        test_response = test()

        return TestResult("deepseek_integration", True, f"API working: {response[:30]}...")
    except Exception as e:
        return TestResult("deepseek_integration", False, str(e))

def test_file_operations() -> TestResult:
    """Test file operations with proper type hints."""
    try:
        test_file: Path = Path("temp_test.txt")

        # Write to file
        with open(test_file, "w") as f:
            f.write("IntelliSense test")

        # Read from file
        with open(test_file, "r") as f:
            content: str = f.read()

        # Clean up
        test_file.unlink()

        return TestResult("file_operations", True, f"File content: {content}")
    except Exception as e:
        return TestResult("file_operations", False, str(e))

def main() -> None:
    """Main function to run all tests."""
    print("🚀 Testing VS Code IntelliSense and Python features in Mexel")
    print(f"Python: {sys.version}")
    print(f"Executable: {sys.executable}")
    print("-" * 60)

    # Run all tests
    tests = [
        test_basic_python(),
        test_deepseek_integration(),
        test_file_operations()
    ]

    # Report results
    for result in tests:
        status = "✅" if result.success else "❌"
        print(f"{status} {result.name}: {result.message}")

    # Summary
    passed = sum(1 for t in tests if t.success)
    total = len(tests)
    print("-" * 60)
    print(f"Summary: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All Python features and IntelliSense working perfectly!")
    else:
        print("⚠️  Some issues detected")

if __name__ == "__main__":
    main()
