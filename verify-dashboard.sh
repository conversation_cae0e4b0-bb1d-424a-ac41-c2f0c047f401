#!/bin/bash

# Colors for terminal output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[VERIFY]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[!]${NC} $1"
}

print_error() {
    echo -e "${RED}[✗]${NC} $1"
}

# Function to check if a service is running on a port
check_service() {
    local port=$1
    local service_name=$2
    local timeout=${3:-5}
    
    print_status "Checking $service_name on port $port..."
    
    if lsof -i :$port > /dev/null 2>&1; then
        print_success "$service_name is running on port $port"
        return 0
    else
        print_error "$service_name is not running on port $port"
        return 1
    fi
}

# Function to test HTTP endpoint
test_endpoint() {
    local url=$1
    local expected_status=${2:-200}
    local description=$3
    
    print_status "Testing $description: $url"
    
    local response=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout 5 --max-time 10 "$url" 2>/dev/null)
    
    if [ "$response" = "$expected_status" ]; then
        print_success "$description responded with HTTP $response"
        return 0
    else
        print_error "$description failed - HTTP $response (expected $expected_status)"
        return 1
    fi
}

# Function to test JSON endpoint and parse response
test_json_endpoint() {
    local url=$1
    local description=$2
    
    print_status "Testing $description: $url"
    
    local response=$(curl -s --connect-timeout 5 --max-time 10 "$url" 2>/dev/null)
    local http_code=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout 5 --max-time 10 "$url" 2>/dev/null)
    
    if [ "$http_code" = "200" ]; then
        if echo "$response" | python3 -m json.tool > /dev/null 2>&1; then
            print_success "$description returned valid JSON"
            echo "    Response: $response"
            return 0
        else
            print_warning "$description returned HTTP 200 but invalid JSON"
            echo "    Response: $response"
            return 1
        fi
    else
        print_error "$description failed - HTTP $http_code"
        return 1
    fi
}

# Function to check frontend static files
check_frontend_static() {
    print_status "Checking frontend static files..."
    
    local response=$(curl -s --connect-timeout 5 --max-time 10 "http://localhost:3000" 2>/dev/null)
    local http_code=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout 5 --max-time 10 "http://localhost:3000" 2>/dev/null)
    
    if [ "$http_code" = "200" ]; then
        if echo "$response" | grep -q "react" || echo "$response" | grep -q "Mexel" || echo "$response" | grep -q "<!DOCTYPE html>"; then
            print_success "Frontend is serving content"
            return 0
        else
            print_warning "Frontend responded but content may not be correct"
            return 1
        fi
    else
        print_error "Frontend static files not accessible - HTTP $http_code"
        return 1
    fi
}

# Function to check CORS configuration
check_cors() {
    print_status "Testing CORS configuration..."
    
    local cors_response=$(curl -s -H "Origin: http://localhost:3000" \
                               -H "Access-Control-Request-Method: GET" \
                               -H "Access-Control-Request-Headers: Content-Type" \
                               -X OPTIONS \
                               --connect-timeout 5 --max-time 10 \
                               "http://localhost:3001/api/test" 2>/dev/null)
    
    local cors_headers=$(curl -s -I -H "Origin: http://localhost:3000" \
                              --connect-timeout 5 --max-time 10 \
                              "http://localhost:3001/api/test" 2>/dev/null | grep -i "access-control")
    
    if [ ! -z "$cors_headers" ]; then
        print_success "CORS headers are present"
        echo "    Headers: $cors_headers"
        return 0
    else
        print_warning "CORS headers may not be configured correctly"
        return 1
    fi
}

# Function to check environment configuration
check_environment() {
    print_status "Checking environment configuration..."
    
    if [ -f "apps/frontend/.env" ]; then
        local api_url=$(grep "REACT_APP_API_URL" apps/frontend/.env | cut -d'=' -f2)
        local frontend_port=$(grep "PORT" apps/frontend/.env | cut -d'=' -f2)
        
        if [ "$api_url" = "http://localhost:3001" ] && [ "$frontend_port" = "3000" ]; then
            print_success "Frontend environment configured correctly"
        else
            print_warning "Frontend environment may not be configured correctly"
            echo "    API_URL: $api_url (expected: http://localhost:3001)"
            echo "    PORT: $frontend_port (expected: 3000)"
        fi
    else
        print_error "Frontend .env file not found"
    fi
}

# Main verification function
main() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}     MEXEL DASHBOARD VERIFICATION      ${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
    
    local tests_passed=0
    local tests_total=0
    
    # Check if services are running
    echo -e "${YELLOW}1. Service Status Checks${NC}"
    echo "------------------------"
    
    tests_total=$((tests_total + 1))
    if check_service 3001 "Backend API"; then
        tests_passed=$((tests_passed + 1))
    fi
    
    tests_total=$((tests_total + 1))
    if check_service 3000 "Frontend Server"; then
        tests_passed=$((tests_passed + 1))
    fi
    
    echo ""
    
    # Test backend endpoints
    echo -e "${YELLOW}2. Backend API Endpoint Tests${NC}"
    echo "-----------------------------"
    
    tests_total=$((tests_total + 1))
    if test_json_endpoint "http://localhost:3001/" "Root endpoint"; then
        tests_passed=$((tests_passed + 1))
    fi
    
    tests_total=$((tests_total + 1))
    if test_json_endpoint "http://localhost:3001/health" "Health check endpoint"; then
        tests_passed=$((tests_passed + 1))
    fi
    
    tests_total=$((tests_total + 1))
    if test_json_endpoint "http://localhost:3001/api/health" "API health endpoint"; then
        tests_passed=$((tests_passed + 1))
    fi
    
    tests_total=$((tests_total + 1))
    if test_json_endpoint "http://localhost:3001/api/test" "Test endpoint"; then
        tests_passed=$((tests_passed + 1))
    fi
    
    echo ""
    
    # Test frontend
    echo -e "${YELLOW}3. Frontend Tests${NC}"
    echo "-----------------"
    
    tests_total=$((tests_total + 1))
    if check_frontend_static; then
        tests_passed=$((tests_passed + 1))
    fi
    
    echo ""
    
    # Test integration
    echo -e "${YELLOW}4. Integration Tests${NC}"
    echo "--------------------"
    
    tests_total=$((tests_total + 1))
    if check_cors; then
        tests_passed=$((tests_passed + 1))
    fi
    
    tests_total=$((tests_total + 1))
    if check_environment; then
        tests_passed=$((tests_passed + 1))
    fi
    
    echo ""
    
    # Summary
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}            VERIFICATION SUMMARY        ${NC}"
    echo -e "${BLUE}========================================${NC}"
    
    if [ $tests_passed -eq $tests_total ]; then
        print_success "All tests passed! ($tests_passed/$tests_total)"
        echo ""
        echo -e "${GREEN}🎉 Mexel Dashboard is running correctly!${NC}"
        echo -e "${GREEN}📊 Dashboard: http://localhost:3000${NC}"
        echo -e "${GREEN}🔧 API: http://localhost:3001${NC}"
        echo ""
        return 0
    else
        print_warning "Some tests failed ($tests_passed/$tests_total passed)"
        echo ""
        echo -e "${YELLOW}⚠️  Dashboard may have issues. Check the failed tests above.${NC}"
        echo ""
        
        if [ $tests_passed -gt 0 ]; then
            echo -e "${BLUE}Services that are working:${NC}"
            [ $tests_passed -ge 1 ] && echo -e "  ${GREEN}✓${NC} Some components are functional"
        fi
        
        echo ""
        echo -e "${BLUE}Troubleshooting tips:${NC}"
        echo "  1. Make sure both services are running: ./start-dashboard.sh"
        echo "  2. Check for port conflicts: lsof -i :3000 && lsof -i :3001"
        echo "  3. Verify virtual environment: cd apps/backend && source venv/bin/activate"
        echo "  4. Check frontend dependencies: cd apps/frontend && yarn install"
        echo ""
        return 1
    fi
}

# Run verification
main "$@"