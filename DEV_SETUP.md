# Developer Setup Guide

This guide outlines the steps to set up the development environment locally.

## Prerequisites

- **Node.js & npm/Yarn:**  
  Ensure you have Node.js (v18 or later) installed. You can choose between npm or Yarn as your package manager.
- **Python:**  
  Python 3.11 or later for the backend services.
- **PostgreSQL:**  
  Local PostgreSQL installation (v14 or later) for the database.
- **Git:**  
  Git for version control.

## Step 1: Database Setup

1. Install PostgreSQL locally
2. Create a new database:

```bash
createdb mexel_dev
```

## Step 2: Backend Setup

1. Create and activate Python virtual environment:

```bash
python -m venv venv-3.11
source venv-3.11/bin/activate  # On Windows use: .\venv-3.11\Scripts\activate
```

2. Install Python dependencies:

```bash
cd apps/apps/backend
pip install -r requirements.txt
```

3. Run database migrations:

```bash
./run_migration.sh
```

## Step 3: Frontend Setup

1. Install Node.js dependencies:

```bash
yarn install
```

2. Build shared packages:

```bash
yarn workspaces run build
```

## Step 4: Environment Configuration

1. Create local environment files:

- Copy `.env.example` to `.env`
- Update database connection string to use local PostgreSQL
- Configure other environment variables as needed

## Step 5: Running the Application

1. Start the backend server:

```bash
cd apps/apps/backend
python run.py
```

2. Start the frontend development server:

```bash
cd apps/frontend
yarn dev
```

The application should now be running at:

- Frontend: http://localhost:3000
- Backend API: http://localhost:3001

## Development Workflow

1. Always activate the Python virtual environment when working on the backend
2. Run tests before submitting changes:
   ```bash
   yarn test        # Frontend tests
   pytest tests/    # Backend tests
   ```
3. Format code before committing:
   ```bash
   yarn format
   ```
