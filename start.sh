#!/bin/bash

echo "🚀 Starting Mexel Dashboard..."
echo "================================"

# Function to cleanup background processes
cleanup() {
    echo ""
    echo "🛑 Stopping services..."
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null
    fi
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null
    fi
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Check if backend virtual environment exists
if [ ! -d "apps/backend/venv" ]; then
    echo "❌ Backend virtual environment not found!"
    echo "Please run: cd apps/backend && python -m venv venv && source venv/bin/activate && pip install -r requirements.txt"
    exit 1
fi

# Start backend in background
echo "🐍 Starting Python backend (port 3001)..."
cd apps/backend
source venv/bin/activate
python simple_main.py &
BACKEND_PID=$!
cd ../..

# Wait a moment for backend to start
sleep 2

# Start frontend
echo "⚛️  Starting React frontend (port 3000)..."
cd apps/frontend
yarn start &
FRONTEND_PID=$!
cd ../..

echo ""
echo "✅ Services started successfully!"
echo "📱 Frontend: http://localhost:3000"
echo "🔧 Backend:  http://localhost:3001"
echo ""
echo "Press Ctrl+C to stop both services"

# Wait for user to stop
wait
