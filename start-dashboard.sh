#!/bin/bash

# Colors for terminal output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[MEXEL DASHBOARD]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if a port is in use
check_port() {
    local port=$1
    if lsof -i :$port > /dev/null 2>&1; then
        return 0  # Port is in use
    else
        return 1  # Port is free
    fi
}

# Function to kill processes on a specific port
kill_port_processes() {
    local port=$1
    print_status "Checking port $port..."
    
    if check_port $port; then
        print_warning "Port $port is in use. Attempting to free it..."
        
        # Get PIDs using the port
        local pids=$(lsof -t -i :$port 2>/dev/null)
        
        if [ ! -z "$pids" ]; then
            print_status "Killing processes: $pids"
            kill -9 $pids 2>/dev/null
            sleep 2
            
            # Verify port is now free
            if check_port $port; then
                print_error "Failed to free port $port. Please manually stop the conflicting service."
                return 1
            else
                print_success "Port $port is now free"
                return 0
            fi
        fi
    else
        print_success "Port $port is available"
        return 0
    fi
}

# Function to start backend
start_backend() {
    print_status "Starting backend server on port 3001..."
    
    cd apps/backend
    
    # Check if virtual environment exists
    if [ ! -d "venv" ]; then
        print_error "Virtual environment not found at apps/backend/venv"
        print_status "Please create the virtual environment first:"
        print_status "cd apps/backend && python3.11 -m venv venv"
        exit 1
    fi
    
    # Check if simple_main.py exists
    if [ ! -f "simple_main.py" ]; then
        print_error "simple_main.py not found in apps/backend"
        exit 1
    fi
    
    # Activate virtual environment and start server
    source venv/bin/activate
    
    # Check if required packages are installed
    if ! python -c "import fastapi, uvicorn" 2>/dev/null; then
        print_warning "Installing required packages..."
        pip install fastapi uvicorn
    fi
    
    print_status "Launching backend server..."
    python3.11 simple_main.py &
    BACKEND_PID=$!
    
    # Give server time to start
    sleep 3
    
    # Verify backend is running
    if kill -0 $BACKEND_PID 2>/dev/null; then
        print_success "Backend server started successfully (PID: $BACKEND_PID)"
        # Test if API is responding
        if curl -s http://localhost:3001/health > /dev/null; then
            print_success "Backend API is responding at http://localhost:3001"
        else
            print_warning "Backend started but API may not be ready yet"
        fi
    else
        print_error "Failed to start backend server"
        return 1
    fi
    
    cd ../..
    return 0
}

# Function to start frontend
start_frontend() {
    print_status "Starting frontend server on port 3000..."
    
    cd apps/frontend
    
    # Check if package.json exists
    if [ ! -f "package.json" ]; then
        print_error "package.json not found in apps/frontend"
        exit 1
    fi
    
    # Check if node_modules exists
    if [ ! -d "node_modules" ]; then
        print_warning "node_modules not found. Installing dependencies..."
        yarn install
    fi
    
    print_status "Launching frontend server..."
    yarn start &
    FRONTEND_PID=$!
    
    # Give server time to start
    sleep 5
    
    # Verify frontend is running
    if kill -0 $FRONTEND_PID 2>/dev/null; then
        print_success "Frontend server started successfully (PID: $FRONTEND_PID)"
    else
        print_error "Failed to start frontend server"
        return 1
    fi
    
    cd ../..
    return 0
}

# Function to cleanup on exit
cleanup() {
    print_status "Shutting down dashboard services..."
    
    if [ ! -z "$BACKEND_PID" ] && kill -0 $BACKEND_PID 2>/dev/null; then
        print_status "Stopping backend server (PID: $BACKEND_PID)..."
        kill $BACKEND_PID
    fi
    
    if [ ! -z "$FRONTEND_PID" ] && kill -0 $FRONTEND_PID 2>/dev/null; then
        print_status "Stopping frontend server (PID: $FRONTEND_PID)..."
        kill $FRONTEND_PID
    fi
    
    # Kill any remaining processes on our ports
    pkill -f "simple_main.py" 2>/dev/null
    pkill -f "react-scripts start" 2>/dev/null
    
    print_success "Dashboard services stopped"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Main execution
print_status "Starting Mexel Dashboard..."
print_status "================================"
print_status "Frontend: http://localhost:3000"
print_status "Backend API: http://localhost:3001"
print_status "================================"

# Check and free required ports
if ! kill_port_processes 3001; then
    exit 1
fi

if ! kill_port_processes 3000; then
    exit 1
fi

# Start services
if ! start_backend; then
    print_error "Failed to start backend. Exiting."
    exit 1
fi

if ! start_frontend; then
    print_error "Failed to start frontend. Stopping backend and exiting."
    kill $BACKEND_PID 2>/dev/null
    exit 1
fi

# Dashboard is now running
print_success "Mexel Dashboard is now running!"
print_status "================================"
print_status "📊 Dashboard: http://localhost:3000"
print_status "🔧 API: http://localhost:3001"
print_status "❤️  Health Check: http://localhost:3001/health"
print_status "🧪 Test Endpoint: http://localhost:3001/api/test"
print_status "================================"
print_status "Press Ctrl+C to stop all services"

# Wait for services to keep running
wait $BACKEND_PID $FRONTEND_PID