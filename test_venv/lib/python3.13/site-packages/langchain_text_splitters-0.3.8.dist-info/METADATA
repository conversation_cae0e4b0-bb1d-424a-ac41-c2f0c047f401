Metadata-Version: 2.1
Name: langchain-text-splitters
Version: 0.3.8
Summary: LangChain text splitting utilities
License: MIT
Project-URL: Source Code, https://github.com/langchain-ai/langchain/tree/master/libs/text-splitters
Project-URL: Release Notes, https://github.com/langchain-ai/langchain/releases?q=tag%3A%22langchain-text-splitters%3D%3D0%22&expanded=true
Project-URL: repository, https://github.com/langchain-ai/langchain
Requires-Python: <4.0,>=3.9
Requires-Dist: langchain-core<1.0.0,>=0.3.51
Description-Content-Type: text/markdown

# 🦜✂️ LangChain Text Splitters

[![Downloads](https://static.pepy.tech/badge/langchain_text_splitters/month)](https://pepy.tech/project/langchain_text_splitters)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

## Quick Install

```bash
pip install langchain-text-splitters
```

## What is it?

LangChain Text Splitters contains utilities for splitting into chunks a wide variety of text documents.

For full documentation see the [API reference](https://python.langchain.com/api_reference/text_splitters/index.html)
and the [Text Splitters](https://python.langchain.com/docs/modules/data_connection/document_transformers/) module in the main docs.

## 📕 Releases & Versioning

`langchain-text-splitters` is currently on version `0.0.x`.

Minor version increases will occur for:

- Breaking changes for any public interfaces NOT marked `beta`

Patch version increases will occur for:

- Bug fixes
- New features
- Any changes to private interfaces
- Any changes to `beta` features

## 💁 Contributing

As an open-source project in a rapidly developing field, we are extremely open to contributions, whether it be in the form of a new feature, improved infrastructure, or better documentation.

For detailed information on how to contribute, see the [Contributing Guide](https://python.langchain.com/docs/contributing/).
