/* eslint-disable no-console */
// Simple script to connect to the server using AuthService
import { authService } from './frontend/src/services/AuthService';

async function runAuthServiceTests() {
  console.log('Testing AuthService...');

  try {
    console.log('1. Starting server connection');
    const result = await authService.startServer();
    console.log('Server connection result:', result);

    console.log('2. Checking if user is logged in');
    const isLoggedIn = authService.isLoggedIn();
    console.log('Is user logged in?', isLoggedIn);

    console.log('3. Checking if service is initialized');
    const isInitialized = authService.isInitialized();
    console.log('Is service initialized?', isInitialized);

    console.log('Tests completed successfully');
  } catch (error) {
    console.error('Error during tests:', error);
  }
}

runAuthServiceTests();
