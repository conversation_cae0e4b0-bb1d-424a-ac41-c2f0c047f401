# Mexel Lead Discovery System - Comprehensive Overview

## Table of Contents

1. [System Overview](#system-overview)
2. [Architecture](#architecture)
3. [Core Components](#core-components)
4. [Product Integration](#product-integration)
5. [User Personas](#user-personas)
6. [Technical Stack](#technical-stack)
7. [API Architecture](#api-architecture)

## System Overview

The Mexel Lead Discovery System is an AI-powered marketing platform specializing in tender monitoring and lead generation for chemical products. The system automates the discovery, analysis, and management of tender opportunities while providing intelligent lead scoring and qualification.

## Architecture

```mermaid
flowchart TB
    subgraph Client["Client Layer"]
        Web["Web Dashboard\n(React)"]
        Mobile["Mobile App\n(React Native)"]
        API["API Clients"]
    end

    subgraph Gateway["API Gateway Layer"]
        REST["REST API"]
        WS["WebSocket"]
        GQL["GraphQL API"]
    end

    subgraph Services["Service Layer"]
        DC["Data Collection\nService"]
        PE["Processing\nEngine"]
        NS["Notification\nService"]
    end

    subgraph Data["Data Layer"]
        DB[("SQLite DB")]
        Cache["Cache Layer"]
        FS["File Storage"]
    end

    Client --> Gateway
    Gateway --> Services
    Services --> Data
```

## Core Components

### Data Flow Architecture

```mermaid
sequenceDiagram
    participant Source as Tender Source
    participant Collector as Data Collector
    participant Engine as Processing Engine
    participant DB as Database
    participant Client as Client

    Source->>Collector: Raw Tender Data
    Collector->>Engine: Validated Data
    Engine->>DB: Processed Data
    DB->>Client: Real-time Updates
```

### Lead Processing Pipeline

```mermaid
stateDiagram-v2
    [*] --> NewLead
    NewLead --> Validation
    Validation --> Scoring
    Scoring --> Notification
    Notification --> CRMUpdate
    CRMUpdate --> [*]

    state Scoring {
        [*] --> TechnicalAnalysis
        TechnicalAnalysis --> ValueAssessment
        ValueAssessment --> RiskAnalysis
        RiskAnalysis --> [*]
    }
```

## Product Integration

The system is optimized for Mexel's core product categories:

- Film Forming Agents (FFAs)
- Surfactants
- Biocides
- Dispersants

### Product-Tender Matching Engine

```mermaid
flowchart LR
    subgraph Input["Input Sources"]
        TD["Tender Document"]
        PS["Product Specs"]
        HR["Historical Results"]
    end

    subgraph Analysis["Analysis Engine"]
        TE["Text Extraction"]
        KM["Keyword Matching"]
        RA["Relevance Analysis"]
    end

    subgraph Output["Output"]
        Score["Match Score"]
        Recom["Recommendations"]
        Risk["Risk Factors"]
    end

    Input --> Analysis
    Analysis --> Output
```

## User Personas

The system serves multiple user types with specialized interfaces:

1. **Sales Managers**

   - Tender opportunity tracking
   - Pipeline management
   - Performance analytics

2. **Technical Specialists**

   - Specification analysis
   - Technical feasibility assessment
   - Regulatory compliance monitoring

3. **Executive Leadership**
   - Business performance monitoring
   - Strategic decision support
   - Market trend analysis

## Technical Stack

### Backend Architecture

```mermaid
flowchart TB
    subgraph API["API Layer"]
        FastAPI["FastAPI"]
        Socket["Socket.IO"]
    end

    subgraph Core["Core Services"]
        Auth["Authentication"]
        TM["Tender Monitor"]
        LP["Lead Processor"]
        NS["Notification System"]
    end

    subgraph Storage["Data Storage"]
        SQL[("SQLite")]
        Cache["Redis Cache"]
        Files["File System"]
    end

    API --> Core
    Core --> Storage
```

## API Architecture

### Communication Protocols

1. **REST API**

   - CRUD operations
   - Resource management
   - Authentication

2. **WebSocket API**
   - Real-time updates
   - System notifications
   - Live tender monitoring

### Integration Points

```mermaid
flowchart LR
    subgraph External["External Systems"]
        CRM["CRM Systems"]
        TenderSources["Tender Sources"]
        Analytics["Analytics Tools"]
    end

    subgraph API["API Gateway"]
        REST["REST Endpoints"]
        WS["WebSocket Server"]
        Auth["Auth Service"]
    end

    subgraph Internal["Internal Services"]
        Core["Core Services"]
        Processing["Processing Engine"]
        Storage["Data Storage"]
    end

    External <--> API
    API <--> Internal
```

This comprehensive overview provides a foundation for understanding the Mexel Lead Discovery System's architecture, components, and integration points. The system is designed to be scalable, maintainable, and aligned with business objectives while serving the needs of diverse user personas.
